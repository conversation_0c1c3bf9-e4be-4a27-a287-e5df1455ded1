{"ID_Faturamento": {"method": "iqr", "threshold": {"low": -52050678973113.5, "high": 96033932725974.5}, "justification": "p=0.000 ≤ 0.05; usar IQR (robusto)"}, "id_loja": {"method": "iqr", "threshold": {"low": -28260769015720.0, "high": 97653111040496.0}, "justification": "p=0.000 ≤ 0.05; usar IQR (robusto)"}, "ID_Vendedor": {"method": "iqr", "threshold": {"low": 63952.5, "high": 86300.5}, "justification": "p=0.000 ≤ 0.05; usar IQR (robusto)"}, "ID_Cliente": {"method": "iqr", "threshold": {"low": 12463395.0, "high": 24720947.0}, "justification": "p=0.000 ≤ 0.05; usar IQR (robusto)"}, "id_produto": {"method": "iqr", "threshold": {"low": 4357.0, "high": 401869.0}, "justification": "p=0.000 ≤ 0.05; usar IQR (robusto)"}, "Documento": {"method": "iqr", "threshold": {"low": -11642.75, "high": 23571.25}, "justification": "p=0.000 ≤ 0.05; usar IQR (robusto)"}}