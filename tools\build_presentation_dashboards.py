from pathlib import Path
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import matplotlib as mpl

BASE = Path('.')
REPORTS = BASE/'reports'/'2025-08-15'
TABLES = REPORTS/'tables'
PLOTS = REPORTS/'plots'
PRESENT = PLOTS/'presentation'
PLOTS.mkdir(parents=True, exist_ok=True)
PRESENT.mkdir(parents=True, exist_ok=True)

# Load cluster names
names_path = TABLES/'cluster_business_names.csv'
if names_path.exists():
    names_df = pd.read_csv(names_path)
else:
    # fallback from assignments
    lab = pd.read_csv(TABLES/'cluster_assignments.csv')
    u = sorted(lab['cluster'].unique())
    names_df = pd.DataFrame({'cluster': u, 'business_name': [f'Cluster {int(x)}' for x in u]})
name_map = {int(r.cluster): r.business_name for _, r in names_df.iterrows()}

# Metrics
metrics = pd.read_csv(TABLES/'supervised_by_cluster_metrics_ci.csv')
metrics['business_name'] = metrics['cluster'].map(name_map)

# Region distribution
DATA = BASE/'data'/'processed'/'features_engineered_regional.csv'
df = pd.read_csv(DATA, low_memory=False)
reg_cols = [c for c in df.columns if c.startswith('Dim_Lojas.REGIAO_CHILLI_')]
idx = np.argmax(df[reg_cols].values, axis=1)
regions = [reg_cols[i].split('REGIAO_CHILLI_')[-1] for i in idx]
lab = pd.read_csv(TABLES/'cluster_assignments.csv')
labels = lab['cluster'].values
reg_df = pd.DataFrame({'cluster': labels, 'REGIAO_CHILLI': regions})
reg_share = (reg_df.groupby(['REGIAO_CHILLI','cluster']).size().rename('count')
             .reset_index())

# Palette consistent with map (tab20 distinct colors)
palette = mpl.colormaps.get_cmap('tab20') if hasattr(mpl, 'colormaps') else mpl.cm.get_cmap('tab20')
cluster_colors = {int(c): mpl.colors.to_hex(palette(int(i)%20)) for i, c in enumerate(sorted(metrics['cluster'].unique()))}

# 1) Metrics bar charts
fig, axes = plt.subplots(1, 3, figsize=(16, 5))
for ax, col, title in zip(axes, ['r2','rmse','mae'], ['R² por Cluster','RMSE por Cluster','MAE por Cluster']):
    m = metrics.sort_values('cluster')
    ax.bar([name_map[int(c)] for c in m['cluster']], m[col], color=[cluster_colors[int(c)] for c in m['cluster']])
    ax.set_title(title)
    ax.set_xticklabels(ax.get_xticklabels(), rotation=45, ha='right')
    ax.grid(axis='y', alpha=0.3)
plt.tight_layout()
fig.savefig(PRESENT/'cluster_metrics_bars.png', dpi=320)
plt.close(fig)

# 2) Regional distribution donut per cluster (stacked donut)
reg_pivot = reg_share.pivot(index='REGIAO_CHILLI', columns='cluster', values='count').fillna(0)
reg_pivot = reg_pivot[sorted(reg_pivot.columns)]
fig2, ax2 = plt.subplots(1, 1, figsize=(8, 8))
outer_sizes = reg_pivot.sum(axis=0).values
ax2.pie(outer_sizes, labels=[name_map[int(c)] for c in reg_pivot.columns],
        colors=[cluster_colors[int(c)] for c in reg_pivot.columns], radius=1.0,
        wedgeprops=dict(width=0.3, edgecolor='white'))
# Inner ring by region share for top cluster by size
top_c = int(reg_pivot.sum(axis=0).idxmax())
inner = (reg_pivot[top_c] / reg_pivot[top_c].sum()).reindex(reg_pivot.index).fillna(0)
ax2.pie(inner, labels=[r.title() for r in reg_pivot.index], radius=0.7,
        colors=sns.color_palette('pastel', n_colors=len(inner)),
        wedgeprops=dict(width=0.3, edgecolor='white'))
ax2.set_title('Distribuição Geográfica dos Clusters (anel externo) e do Cluster Líder por Região (anel interno)')
fig2.savefig(PRESENT/'cluster_geo_donut.png', dpi=320)
plt.close(fig2)

# 3) Business impact summary table (KPIs por cluster)
imp_cols = ['cluster','business_name','n','r2','rmse','mae']
impact = metrics[['cluster','r2','rmse','mae','n']].copy()
impact['business_name'] = impact['cluster'].map(name_map)
impact = impact[imp_cols].sort_values('cluster')
fig3, ax3 = plt.subplots(1,1, figsize=(12, 2 + 0.35*len(impact)))
ax3.axis('off')
# build table
cell_text = impact.round(3).values.tolist()
col_labels = ['Cluster','Nome de Negócio','N','R²','RMSE','MAE']
tbl = ax3.table(cellText=cell_text, colLabels=col_labels, loc='center')
tbl.auto_set_font_size(False)
tbl.set_fontsize(10)
tbl.scale(1, 1.2)
ax3.set_title('Resumo Executivo — KPIs por Cluster')
fig3.savefig(PRESENT/'business_impact_table.png', dpi=320, bbox_inches='tight')
plt.close(fig3)

# 4) Integrated dashboard layout
#   - We will import the enhanced map PNG and assemble in a multi-panel grid
from PIL import Image
map_png = PLOTS/'territorial_clustering_map_multi.png'
try:
    map_im = Image.open(map_png).convert('RGB')
except Exception:
    map_im = Image.new('RGB', (1600, 1200), color=(255,255,255))

met_img = Image.open(PRESENT/'cluster_metrics_bars.png').convert('RGB')
don_img = Image.open(PRESENT/'cluster_geo_donut.png').convert('RGB')
tab_img = Image.open(PRESENT/'business_impact_table.png').convert('RGB')

# Canvas
W = max(map_im.size[0], 1800)
H = int(map_im.size[1] * 1.25)
canvas = Image.new('RGB', (W, H), color=(255,255,255))

# Paste map large on top-left (two-thirds width)
map_scaled = map_im.resize((int(W*0.62), int(H*0.62)))
canvas.paste(map_scaled, (40, 20))

# Paste metrics bar top-right
mW = int(W*0.32)
mH = int(H*0.28)
canvas.paste(met_img.resize((mW, mH)), (W - mW - 40, 20))

# Paste donut bottom-left below map
canvas.paste(don_img.resize((int(W*0.35), int(H*0.28))), (40, int(H*0.68)))

# Paste table bottom-right
canvas.paste(tab_img.resize((int(W*0.58), int(H*0.28))), (int(W*0.40), int(H*0.68)))

# Footer notes and callouts
import PIL.ImageDraw as ImageDraw
import PIL.ImageFont as ImageFont
D = ImageDraw.Draw(canvas)
footer = 'Fonte: features_engineered_regional.csv; Métricas: supervised_by_cluster_metrics_ci.csv; Metodologia: dominância geográfica + desempenho (R² cor, tamanho ~ N).'
D.text((40, H-28), footer, fill=(60,60,60))
callout = 'So What? Concentração relevante em SP; clusters 0 e 3 com R² >= 0,74; priorizar ações segmentadas no Sudeste e Sul.'
D.rectangle([W-680, H-140, W-40, H-40], fill=(245,245,245), outline=None)
D.text((W-660, H-128), callout, fill=(20,20,20))

canvas.save(PRESENT/'dashboard_mapa_metricas.png', dpi=(320,320))
print('Saved dashboard ->', PRESENT/'dashboard_mapa_metricas.png')

