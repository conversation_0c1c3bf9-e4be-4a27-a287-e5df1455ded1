from pathlib import Path
import pandas as pd
import numpy as np
import unicodedata

BASE = Path(__file__).resolve().parents[1]
RAW_XLSX = BASE / 'data' / 'raw' / 'NOVA-Chilli Beans - Inteli - Base de Dados.xlsx'
PROCESSED = BASE / 'data' / 'processed' / 'features_engineered.csv'
OUT_REGIONAL = BASE / 'data' / 'processed' / 'features_engineered_regional.csv'
MAP_OUT = BASE / 'data' / 'processed' / 'region_mapping.csv'
TABLES_DIR = BASE / 'reports' / '2025-08-15' / 'tables'
TABLES_DIR.mkdir(parents=True, exist_ok=True)

STATE_NAME_TO_UF = {
    'acre': 'AC','alagoas': 'AL','amapa': 'AP','amazonas': 'AM','bahia': 'BA','ceara': 'CE','distrito federal': 'DF',
    'espirito santo': 'ES','goias': 'GO','maranhao': 'MA','mato grosso': 'MT','mato grosso do sul': 'MS',
    'minas gerais': 'MG','para': 'PA','paraiba': 'PB','parana': 'PR','pernambuco': 'PE','piaui': 'PI','rio de janeiro': 'RJ',
    'rio grande do norte': 'RN','rio grande do sul': 'RS','rondonia': 'RO','roraima': 'RR','santa catarina': 'SC',
    'sao paulo': 'SP','sergipe': 'SE','tocantins': 'TO'
}


def normalize_text(s: str) -> str:
    if pd.isna(s):
        return ''
    s = str(s)
    s = unicodedata.normalize('NFKD', s).encode('ascii', 'ignore').decode('ascii')
    return s.strip().lower()


def to_uf(val) -> str:
    if pd.isna(val):
        return ''
    s = str(val).strip()
    if len(s) == 2 and s.isalpha():
        return s.upper()
    n = normalize_text(s)
    return STATE_NAME_TO_UF.get(n, '')


def main():
    print('DEBUG: starting prepare_regional_dataset')
    print('DEBUG: RAW_XLSX exists?', RAW_XLSX.exists(), '| path=', RAW_XLSX)
    print('DEBUG: PROCESSED exists?', PROCESSED.exists(), '| path=', PROCESSED)
    if not RAW_XLSX.exists():
        raise SystemExit(f'Raw Excel not found: {RAW_XLSX}')
    if not PROCESSED.exists():
        raise SystemExit(f'Processed dataset not found: {PROCESSED}')

    print('Loading raw Excel columns for mapping...')
    usecols = [
        'Dim_Lojas.Cod_Franqueado', 'Dim_Lojas.Cidade_Emp', 'Dim_Lojas.Estado_Emp', 'Dim_Lojas.Regiao',
        'Dim_Lojas.REGIAO_CHILLI', 'Dim_Cliente.Cidade_cliente', 'Dim_Cliente.Uf_Cliente'
    ]
    try:
        df_raw = pd.read_excel(RAW_XLSX, sheet_name='Fato_Faturamento', usecols=usecols, dtype=str, engine='openpyxl')
    except Exception as e:
        print('ERROR reading Excel with openpyxl:', e)
        df_raw = pd.read_excel(RAW_XLSX, sheet_name='Fato_Faturamento', usecols=usecols, dtype=str)
    print('DEBUG: df_raw shape:', df_raw.shape)

    # Derive UF preference: prefer Estado_Emp if present, else Uf_Cliente
    df_raw['UF_from_loja'] = df_raw['Dim_Lojas.Estado_Emp'].apply(to_uf) if 'Dim_Lojas.Estado_Emp' in df_raw.columns else ''
    df_raw['UF_from_cliente'] = df_raw['Dim_Cliente.Uf_Cliente'].apply(to_uf) if 'Dim_Cliente.Uf_Cliente' in df_raw.columns else ''
    df_raw['UF'] = df_raw['UF_from_loja']
    mask_empty = df_raw['UF'] == ''
    df_raw.loc[mask_empty, 'UF'] = df_raw.loc[mask_empty, 'UF_from_cliente']

    # City preference: loja, else cliente
    df_raw['Cidade'] = df_raw['Dim_Lojas.Cidade_Emp']
    mask_city = df_raw['Cidade'].isna() | (df_raw['Cidade'].astype(str).str.strip() == '')
    if 'Dim_Cliente.Cidade_cliente' in df_raw.columns:
        df_raw.loc[mask_city, 'Cidade'] = df_raw.loc[mask_city, 'Dim_Cliente.Cidade_cliente']

    # Region fields
    df_raw['Regiao_Loja'] = df_raw.get('Dim_Lojas.Regiao', '')
    df_raw['Regiao_Chilli'] = df_raw.get('Dim_Lojas.REGIAO_CHILLI', '')

    # Build mapping by Cod_Franqueado (most frequent UF per store)
    if 'Dim_Lojas.Cod_Franqueado' not in df_raw.columns:
        raise SystemExit('Column Dim_Lojas.Cod_Franqueado not found in raw data')

    def mode_or_first(series):
        s = series.dropna().astype(str)
        if s.empty:
            return ''
        return s.mode().iloc[0] if not s.mode().empty else s.iloc[0]

    group_cols = ['Dim_Lojas.Cod_Franqueado']
    agg = {
        'UF': mode_or_first,
        'Cidade': mode_or_first,
        'Regiao_Loja': mode_or_first,
        'Regiao_Chilli': mode_or_first,
    }
    mapping = df_raw.groupby(group_cols).agg(agg).reset_index()

    # Save mapping CSV
    MAP_OUT.parent.mkdir(parents=True, exist_ok=True)
    mapping.to_csv(MAP_OUT, index=False)
    print('Saved mapping to', MAP_OUT)

    # Enrich processed dataset
    df_proc = pd.read_csv(PROCESSED)
    key = 'Dim_Lojas.Cod_Franqueado'
    if key not in df_proc.columns:
        raise SystemExit(f'Key {key} not in processed dataset; cannot join region mapping')

    # ensure same dtype for join key
    df_proc[key] = df_proc[key].astype(str)
    mapping[key] = mapping[key].astype(str)

    df_enriched = df_proc.merge(mapping, on=key, how='left')

    # If coverage is poor, attempt rank-based key reconstruction (monotonic scaler)
    def compute_coverage(df):
        uf_series = df['UF'] if 'UF' in df.columns else pd.Series([], dtype=object)
        missing_mask = uf_series.isna() | (uf_series.astype(str).str.strip() == '')
        missing = int(missing_mask.sum())
        return 100.0 * (1.0 - (missing / max(1, len(df))))

    coverage = compute_coverage(df_enriched)
    print(f'Initial region coverage: {coverage:.2f}%')

    if coverage < 90.0:
        print('Low coverage detected. Trying rank-based mapping...')
        # Build rank mapping between FE scaled values and original integer-like codes
        fe_vals = pd.Series(df_proc[key].unique()).sort_values().reset_index(drop=True)
        # Ensure mapping codes are treated as integer strings to avoid '400000.0' vs '400000' mismatches
        mp_vals = pd.to_numeric(mapping[key], errors='coerce').dropna().astype(int).sort_values().reset_index(drop=True)
        if len(fe_vals) == len(mp_vals) and len(fe_vals) > 0:
            # map FE scaled float -> original code (string) by rank
            rank_map = dict(zip(fe_vals.tolist(), mp_vals.astype(str).tolist()))
            df_proc['__orig_code_by_rank__'] = df_proc[key].map(rank_map).astype(str)
            mapping_rank = mapping.rename(columns={key: '__orig_code_by_rank__'}).copy()
            # normalize as integer strings, too
            mapping_rank['__orig_code_by_rank__'] = pd.to_numeric(mapping_rank['__orig_code_by_rank__'], errors='coerce').astype('Int64').astype(str)
            df_enriched = df_proc.merge(mapping_rank, on='__orig_code_by_rank__', how='left')
            coverage = compute_coverage(df_enriched)
            print(f'Rank-based coverage: {coverage:.2f}%')
        else:
            print('Rank mapping not possible (cardinality mismatch).')

    # Save enriched dataset
    df_enriched.to_csv(OUT_REGIONAL, index=False)
    print('Saved enriched dataset to', OUT_REGIONAL)

    # Data dictionary (regional subset)
    dict_rows = []
    for c in usecols:
        dict_rows.append({'source_sheet': 'Fato_Faturamento', 'source_column': c, 'mapped_to': c.split('.')[-1]})
    dict_rows += [
        {'source_sheet': 'Fato_Faturamento', 'source_column': 'UF_from_loja', 'mapped_to': 'UF (prefer loja)'},
        {'source_sheet': 'Fato_Faturamento', 'source_column': 'UF_from_cliente', 'mapped_to': 'UF (fallback cliente)'}
    ]
    dd = pd.DataFrame(dict_rows)
    dd.to_csv(TABLES_DIR / 'data_dictionary_regional.csv', index=False)
    print('Saved data dictionary to', TABLES_DIR / 'data_dictionary_regional.csv')

if __name__ == '__main__':
    main()

