from pathlib import Path
import pandas as pd
import numpy as np
from sklearn.preprocessing import OneHotEncoder, StandardScaler
from sklearn.impute import SimpleImputer
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.cluster import KMeans

BASE = Path('.')
REPORTS = BASE/'reports'/'2025-08-15'
TABLES = REPORTS/'tables'
TABLES.mkdir(parents=True, exist_ok=True)

DATA = BASE/'data'/'processed'/'features_engineered_regional.csv'
df = pd.read_csv(DATA, low_memory=False)

# Anti-leakage: remove target and derivatives from numeric feature block
TARGET = 'valor' if 'valor' in df.columns else None
base_targets = [t for t in [TARGET,'valor','revenue','sales','y'] if t]

def _is_leak(c:str)->bool:
    cl=c.lower(); pats=[]
    for t in base_targets:
        t=t.lower(); pats += [t, f'_{t}', f'{t}_', f'sq_{t}', f'{t}_boxcox', f'{t}_log', f'{t}_log1p', f'{t}_sqrt', f'log_{t}', f'boxcox_{t}']
    return any(p in cl for p in pats)

num_cols = [c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]
num_clean = [c for c in num_cols if not _is_leak(c) and c not in base_targets]

# Territorial categorical
cat_cols = []
if 'UF' in df.columns and df['UF'].notna().any():
    cat_cols.append('UF')
reg_oh = [c for c in df.columns if 'REGIAO_CHILLI' in c]

transformers = []
if cat_cols:
    transformers.append(('cat', Pipeline([
        ('imp', SimpleImputer(strategy='most_frequent')),
        ('ohe', OneHotEncoder(handle_unknown='ignore'))
    ]), cat_cols))
if num_clean:
    transformers.append(('num', Pipeline([
        ('imp', SimpleImputer(strategy='median')),
        ('sc', StandardScaler(with_mean=False))
    ]), num_clean))
if reg_oh:
    transformers.append(('reg', 'passthrough', reg_oh))

pre = ColumnTransformer(transformers)
X = pre.fit_transform(df)

k = 5 if len(df) > 2000 else 4
km = KMeans(n_clusters=k, random_state=42, n_init=10)
labels = km.fit_predict(X)

assign = pd.DataFrame({'index': np.arange(len(labels)), 'cluster': labels})
assign.to_csv(TABLES/'cluster_assignments.csv', index=False)
print('Saved:', TABLES/'cluster_assignments.csv', 'shape=', assign.shape)

