from pathlib import Path
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score, silhouette_samples

# Local import (run from repo root)
from feature_filter import compute_clean_features  # type: ignore

# Paths
BASE = Path('.')
REPORTS = BASE/'reports'/'2025-08-15'
TABLES = REPORTS/'tables'
PLOTS = REPORTS/'plots'/'presentation'/'slides'
PLOTS.mkdir(parents=True, exist_ok=True)
TABLES.mkdir(parents=True, exist_ok=True)
DATA = BASE/'data'/'processed'/'features_engineered_regional.csv'

# Load data
df = pd.read_csv(DATA, low_memory=False)
TARGET = 'valor' if 'valor' in df.columns else df.select_dtypes('number').columns[-1]

# Helper: UF -> macro-região e centroides
UF_TO_REGION = {
    'AC':'Norte','AL':'Nordeste','AP':'Norte','AM':'Norte','BA':'Nordeste','CE':'Nordeste','DF':'Centro-Oeste',
    'ES':'Sudeste','GO':'Centro-Oeste','MA':'Nordeste','MT':'Centro-Oeste','MS':'Centro-Oeste','MG':'Sudeste',
    'PA':'Norte','PB':'Nordeste','PR':'Sul','PE':'Nordeste','PI':'Nordeste','RJ':'Sudeste','RN':'Nordeste',
    'RS':'Sul','RO':'Norte','RR':'Norte','SC':'Sul','SP':'São Paulo','SE':'Nordeste','TO':'Norte'}
UF_CENTROID = {
    'AC':(-70.0,-9.0),'AL':(-36.6,-9.6),'AP':(-51.8,1.4),'AM':(-64.7,-3.4),'BA':(-41.7,-12.5),'CE':(-39.6,-5.3),
    'DF':(-47.9,-15.8),'ES':(-40.3,-19.5),'GO':(-49.3,-15.9),'MA':(-45.2,-5.0),'MT':(-56.1,-12.6),'MS':(-54.2,-20.5),
    'MG':(-44.6,-18.5),'PA':(-52.8,-3.8),'PB':(-36.7,-7.2),'PR':(-51.5,-24.5),'PE':(-37.9,-8.4),'PI':(-42.7,-7.0),
    'RJ':(-42.6,-22.1),'RN':(-36.8,-5.7),'RS':(-53.2,-30.0),'RO':(-63.4,-10.8),'RR':(-61.3,2.0),'SC':(-50.9,-27.2),
    'SP':(-48.0,-22.1),'SE':(-37.4,-10.5),'TO':(-48.3,-10.2)}

# Build geo features
uf_series = df['UF'].astype(str) if 'UF' in df.columns else pd.Series(['NA']*len(df))
macro = uf_series.map(UF_TO_REGION).fillna('NA')
# One-hots: macro-região (forte), UF (cap 10 mais frequentes)
macro_oh = pd.get_dummies(macro, prefix='REG', dtype=float)
uf_counts = uf_series.value_counts()
top_ufs = set(uf_counts.head(10).index.tolist())
uf_capped = uf_series.where(uf_series.isin(top_ufs), other='OUTRAS')
uf_oh = pd.get_dummies(uf_capped, prefix='UF', dtype=float)
# Coordenadas
lonlat = uf_series.map(lambda u: UF_CENTROID.get(u, (np.nan, np.nan)))
lon = pd.Series([x[0] for x in lonlat], name='lon')
lat = pd.Series([x[1] for x in lonlat], name='lat')
coords = pd.concat([lon, lat], axis=1).fillna(0.0)

# Behavioral features (anti-vazamento), removendo sinais explícitos de região para não duplicar
clean_cols, _ = compute_clean_features(df, TARGET, REPORTS)
beh_cols = [c for c in clean_cols if ('REGIAO_CHILLI' not in c) and (c != 'UF')]
X_beh = df[beh_cols].select_dtypes(include=['number']).fillna(0.0)

# Scale por bloco e aplicar pesos (geografia dominante)
sc_macro = StandardScaler(with_mean=False).fit_transform(macro_oh)
sc_uf = StandardScaler(with_mean=False).fit_transform(uf_oh)
sc_coords = StandardScaler(with_mean=False).fit_transform(coords)
sc_beh = StandardScaler(with_mean=False).fit_transform(X_beh)

# Converter para sparse e empilhar
from scipy import sparse

def to_sparse(a):
    return a if sparse.issparse(a) else sparse.csr_matrix(a)

W_MACRO, W_UF, W_COORDS, W_BEH = 3.0, 2.0, 1.5, 1.0
X_final = sparse.hstack([
    to_sparse(sc_macro) * W_MACRO,
    to_sparse(sc_uf) * W_UF,
    to_sparse(sc_coords) * W_COORDS,
    to_sparse(sc_beh) * W_BEH
], format='csr')

# Seleção de K pelo silhouette (priorizando solução próxima a 5)
Ks = [4,5,6,7]
best_k, best_sil = None, -1
for k in Ks:
    km = KMeans(n_clusters=k, random_state=42, n_init=10)
    labels_k = km.fit_predict(X_final)
    try:
        sil = float(silhouette_score(X_final, labels_k)) if len(set(labels_k))>1 else -1
    except Exception:
        sil = -1
    if sil > best_sil or (abs(sil-best_sil)<1e-6 and k==5):
        best_k, best_sil = k, sil

km = KMeans(n_clusters=best_k, random_state=42, n_init=10).fit(X_final)
labels = km.labels_

# Salvar novas atribuições (sem sobrescrever as antigas)
assign_path = TABLES/'cluster_assignments_territorial.csv'
pd.DataFrame({'index': np.arange(len(labels)), 'cluster': labels}).to_csv(assign_path, index=False)
print('Saved territorial clustering ->', assign_path, 'k=', best_k, 'silhouette=', round(best_sil,3))

# Silhouette por cluster
try:
    sil_s = silhouette_samples(X_final, labels)
    sil_by_c = {int(c): float(np.mean(sil_s[np.array(labels)==c])) for c in sorted(set(labels))}
except Exception:
    sil_s = np.array([])
    sil_by_c = {}

# Distribuição e percentuais
vc = pd.Series(labels).value_counts().sort_index()
perc = (vc / vc.sum() * 100).round(1)

# Gráfico: distribuição
fig, ax = plt.subplots(figsize=(12,7))
ax.bar([f'Cluster {i}' for i in vc.index], vc.values, color=sns.color_palette('tab20', len(vc)))
for i, (n, p) in enumerate(zip(vc.values, perc.values)):
    ax.text(i, n*1.01, f'{p:.1f}%', ha='center', va='bottom')
ax.set_title('Distribuição de Clusters (territorial)'); ax.set_ylabel('N casos'); ax.tick_params(axis='x', rotation=30)
ax.grid(True, axis='y', alpha=0.3)
plt.tight_layout(); plt.savefig(PLOTS/'clusters_territorial_distribution_v1.png', dpi=320); plt.close()

# Silhouette box por cluster
if sil_s.size>0:
    data = [sil_s[np.array(labels)==c] for c in sorted(set(labels))]
    fig, ax = plt.subplots(figsize=(12,7))
    bp = ax.boxplot(data, labels=[f'Cluster {c}' for c in sorted(set(labels))], patch_artist=True)
    for patch, color in zip(bp['boxes'], sns.color_palette('tab20', len(data))):
        patch.set_facecolor(color); patch.set_alpha(0.7)
    ax.axhline(0, color='k', lw=1, ls='--')
    ax.set_title(f'Silhouette por Cluster (territorial) — média global = {best_sil:.3f}')
    ax.set_ylabel('Coeficiente de Silhouette'); ax.grid(True, axis='y', alpha=0.3)
    plt.tight_layout(); plt.savefig(PLOTS/'clusters_territorial_silhouette_box_v1.png', dpi=320); plt.close()

# Heatmap de características por cluster (top10 correlacionadas ao target)
corr_with_target = df[beh_cols].corrwith(pd.to_numeric(df[TARGET], errors='coerce')).abs().sort_values(ascending=False)
top10 = corr_with_target.head(10).index.tolist() if corr_with_target.notna().any() else beh_cols[:10]
means = df.groupby(pd.Series(labels, name='cluster'))[top10].mean()
means_norm = (means - means.min()) / (means.max() - means.min() + 1e-9)
plt.figure(figsize=(max(12, len(top10)*0.9), 6))
sns.heatmap(means_norm, annot=False, cmap='YlGnBu', cbar_kws={'label':'Média normalizada'})
plt.title('Resumo de Características por Cluster (territorial) — Top 10 variáveis')
plt.xlabel('Variáveis'); plt.ylabel('Cluster')
plt.tight_layout(); plt.savefig(PLOTS/'clusters_territorial_characteristics_heatmap_v1.png', dpi=320); plt.close()

# Tabela: Cluster -> UFs dominantes
uf_df = pd.DataFrame({'UF': uf_series, 'cluster': labels})
uf_dom = uf_df.groupby(['cluster','UF']).size().rename('n').reset_index()
# Calcular percentuais com merge para evitar problemas de índice
cluster_tot = uf_dom.groupby('cluster')['n'].sum().rename('tot').reset_index()
uf_dom = uf_dom.merge(cluster_tot, on='cluster', how='left')
uf_dom['pct'] = uf_dom['n'] / uf_dom['tot'] * 100
ranked = uf_dom.sort_values(['cluster','n'], ascending=[True, False])
res_rows = []
for c, sub in ranked.groupby('cluster', sort=True):
    top3 = sub.head(3).reset_index(drop=True)
    res_rows.append({
        'cluster': int(c),
        'top_uf_1': f"{top3.loc[0,'UF']}:{top3.loc[0,'pct']:.1f}%" if len(top3)>0 else '',
        'top_uf_2': f"{top3.loc[1,'UF']}:{top3.loc[1,'pct']:.1f}%" if len(top3)>1 else '',
        'top_uf_3': f"{top3.loc[2,'UF']}:{top3.loc[2,'pct']:.1f}%" if len(top3)>2 else ''
    })
geo_table = pd.DataFrame(res_rows).sort_values('cluster')
geo_path = TABLES/'cluster_territorial_geography_summary.csv'
geo_table.to_csv(geo_path, index=False)
print('Saved geography summary ->', geo_path)

# Mapa territorial simples (Plotly, sem dependência externa de topojson)
try:
    import json, requests
    import plotly.graph_objects as go
    import plotly.io as pio
    raw = requests.get('https://raw.githubusercontent.com/codeforamerica/click_that_hood/master/public/data/brazil-states.geojson', timeout=30).text
    geojson = json.loads(raw)
    name_to_uf = {
        'Acre':'AC','Alagoas':'AL','Amapá':'AP','Amazonas':'AM','Bahia':'BA','Ceará':'CE','Distrito Federal':'DF',
        'Espírito Santo':'ES','Goiás':'GO','Maranhão':'MA','Mato Grosso':'MT','Mato Grosso do Sul':'MS','Minas Gerais':'MG',
        'Pará':'PA','Paraíba':'PB','Paraná':'PR','Pernambuco':'PE','Piauí':'PI','Rio de Janeiro':'RJ','Rio Grande do Norte':'RN',
        'Rio Grande do Sul':'RS','Rondônia':'RO','Roraima':'RR','Santa Catarina':'SC','São Paulo':'SP','Sergipe':'SE','Tocantins':'TO'}
    # Dominância por UF
    pivot = uf_df.groupby(['UF','cluster']).size().rename('n').reset_index().pivot(index='UF', columns='cluster', values='n').fillna(0)
    dom = pivot.values.argmax(axis=1)
    uf_order = pivot.index.tolist()
    # Mapear para a sequência das features do geojson
    states = [f['properties']['name'] for f in geojson['features']]
    ufs_for_states = [name_to_uf.get(s, '') for s in states]
    z = []
    for u in ufs_for_states:
        if u in uf_order:
            z.append(int(dom[uf_order.index(u)]))
        else:
            z.append(-1)
    fig = go.Figure(go.Choropleth(
        geojson=geojson, locations=states, z=z,
        featureidkey='properties.name', colorscale='Viridis', showscale=False,
        marker_line_color='white', marker_line_width=0.8,
        hovertemplate='<b>%{location}</b><extra></extra>'
    ))
    fig.update_geos(fitbounds='locations', showcountries=False, showcoastlines=False, projection_type='mercator', visible=True)
    fig.update_layout(width=1600, height=1000, margin=dict(l=20,r=20,t=40,b=20), title='Clusters (territorial) — dominância por UF')
    pio.write_image(fig, str(PLOTS/'territorial_map_geo_clusters_v1.png'), scale=2)
except Exception as e:
    print('Map export skipped:', e)

# Sumário final para logs
print('Territorial clustering complete:', 'k=', best_k, 'silhouette=', round(best_sil,3), 'sil_by_cluster=', {int(k): round(float(v),3) for k,v in sil_by_c.items()})

