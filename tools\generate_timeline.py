from pathlib import Path
import matplotlib.pyplot as plt

REPORTS = Path('reports/2025-08-15')
PLOT_DIR = REPORTS / 'plots' / 'presentation'
PLOT_DIR.mkdir(parents=True, exist_ok=True)

milestones = [
    ('Sprint 1', 'Descoberta de dados e EDA (01–02)'),
    ('Sprint 2', 'Engenharia de atributos e regionalização'),
    ('Sprint 3', 'Correções anti-leakage iniciais'),
    ('Sprint 4', 'Mapa territorial do Brasil (PNG + HTML)'),
    ('Sprint 5', 'Bench wrapper + métricas com IC'),
    ('Sprint 6', 'Endurecimento anti-leak (IDs, corr>0,98)'),
    ('Sprint 7', 'Nomes de negócio para clusters'),
    ('Sprint 8', 'Learning curves + resíduos por cluster'),
    ('Sprint 9', 'Dashboard executivo reexecutado (06)'),
]

fig, ax = plt.subplots(figsize=(12, 3.5))
ax.axhline(0, color='#999999', lw=2)
for i, (sprint, label) in enumerate(milestones, start=1):
    ax.plot(i, 0, 'o', color='#1f77b4')
    ax.text(i, 0.15, f'{sprint}', ha='center', va='bottom', fontsize=10, fontweight='bold')
    ax.text(i, -0.15, label, ha='center', va='top', fontsize=9, wrap=True)

ax.set_ylim(-0.6, 0.6)
ax.axis('off')
ax.set_title('Linha do tempo — Avanço do MVP ChilliAnalyzer', fontsize=13)
plt.tight_layout()
out = PLOT_DIR / 'timeline_milestones.png'
plt.savefig(out, dpi=180, bbox_inches='tight')
print('Saved timeline to', out)

