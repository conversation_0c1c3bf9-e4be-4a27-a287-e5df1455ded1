from __future__ import annotations
from pathlib import Path
from typing import List, Dict, Any
import re
import json
import nbformat as nbf

NOTEBOOKS = [
    Path('notebooks/chilli_beans_analysis.ipynb'),
    Path('notebooks/model_comparison_colab.ipynb'),
    Path('notebooks/preprocessamento/eda_distribuicoes.ipynb'),
    Path('notebooks/preprocessamento/eda_geografico.ipynb'),
]

ID_COLS = ['id_loja', 'id_cliente']

ANTI_ID_HELPER = (
    "# [anti-id] helpers – escolher dimensão de negócio em vez de IDs\n"
    "import pandas as pd\n"
    "SAFE_DIM_PRIORITY = ['uf','UF','cidade','Cidade','Tipo_PDV','tipo_pdv','Estado_Emp','estado_emp']\n"
    "def SAFE_DIM_OF(df_like):\n"
    "    try:\n"
    "        cols = list(df_like.columns)\n"
    "    except Exception:\n"
    "        cols = []\n"
    "    # 1) Prioridade explícita\n"
    "    for c in SAFE_DIM_PRIORITY:\n"
    "        if c in cols:\n"
    "            return c\n"
    "    # 2) Heurística por tipo (object/category)\n"
    "    try:\n"
    "        obj_cols = [c for c in cols if str(getattr(df_like[c], 'dtype', '')) in ('object','category')]\n"
    "        if obj_cols:\n"
    "            return obj_cols[0]\n"
    "    except Exception:\n"
    "        pass\n"
    "    # 3) Fallbacks seguros\n"
    "    for c in ['data']:\n"
    "        if c in cols:\n"
    "            return c\n"
    "    return cols[0] if cols else None\n"
    "# Var global default para compatibilidade com códigos existentes\n"
    "SAFE_DIM = None\n"
    "for _name in ['df_pp','df_mod','df_train','df_clean','df','df_fe','df_tr']:\n"
    "    try:\n"
    "        _obj = globals().get(_name, None)\n"
    "        if _obj is not None:\n"
    "            _dim = SAFE_DIM_OF(_obj)\n"
    "            if _dim is not None:\n"
    "                SAFE_DIM = _dim\n"
    "                break\n"
    "    except Exception:\n"
    "        pass\n"
    "if SAFE_DIM is None:\n"
    "    SAFE_DIM = 'uf'\n"
)

# Regex patterns for replacements
PATTERNS = [
    # groupby patterns
    (re.compile(r"\.groupby\(\s*'id_loja'\s*\)"), ".groupby(SAFE_DIM)"),
    (re.compile(r"\.groupby\(\s*\[\s*'id_loja'\s*\]\s*\)"), ".groupby([SAFE_DIM])"),
    (re.compile(r"\.groupby\(\s*'id_cliente'\s*\)"), ".groupby(SAFE_DIM)"),
    (re.compile(r"\.groupby\(\s*\[\s*'id_cliente'\s*\]\s*\)"), ".groupby([SAFE_DIM])"),
    # pivot_table index/columns
    (re.compile(r"pivot_table\(([^\)]*)index\s*=\s*'id_loja'"), r"pivot_table(\1index=SAFE_DIM"),
    (re.compile(r"pivot_table\(([^\)]*)columns\s*=\s*'id_loja'"), r"pivot_table(\1columns=SAFE_DIM"),
    (re.compile(r"pivot_table\(([^\)]*)index\s*=\s*'id_cliente'"), r"pivot_table(\1index=SAFE_DIM"),
    (re.compile(r"pivot_table\(([^\)]*)columns\s*=\s*'id_cliente'"), r"pivot_table(\1columns=SAFE_DIM"),
    # seaborn keyword args x/hue using id columns
    (re.compile(r"(\bx\s*=\s*)'id_loja'"), r"\1SAFE_DIM"),
    (re.compile(r"(\bhue\s*=\s*)'id_loja'"), r"\1SAFE_DIM"),
    (re.compile(r"(\bx\s*=\s*)'id_cliente'"), r"\1SAFE_DIM"),
    (re.compile(r"(\bhue\s*=\s*)'id_cliente'"), r"\1SAFE_DIM"),
    # plt.plot or generic df['id_loja']
    (re.compile(r"\[[\"']id_loja[\"']\]"), "[SAFE_DIM]"),
    (re.compile(r"\[[\"']id_cliente[\"']\]"), "[SAFE_DIM]"),
]

# Patterns to detect (report only) risky filters that we will not auto-modify
DETECT_ONLY = [
    re.compile(r"\[[^\]]*['\"]id_loja['\"][^\]]*\]"),
    re.compile(r"\[[^\]]*['\"]id_cliente['\"][^\]]*\]"),
]

# Helper transform functions to handle list arguments with object-aware SAFE_DIM_OF

def replace_id_in_list_context(src: str) -> str:
    # groupby([...]) list
    def repl_groupby_list(m: re.Match) -> str:
        var = m.group(1)
        inner = m.group(2)
        inner2 = re.sub(r"['\"]id_loja['\"]", f"SAFE_DIM_OF({var})", inner)
        inner2 = re.sub(r"['\"]id_cliente['\"]", f"SAFE_DIM_OF({var})", inner2)
        return f"{var}.groupby([{inner2}])"
    src = re.sub(r"([A-Za-z_][A-Za-z0-9_]*)\.groupby\(\s*\[([^\]]*?)\]\s*\)", repl_groupby_list, src)

    # pivot_table(index=[...]) and columns=[...]
    def repl_pivot_index(m: re.Match) -> str:
        var, args = m.group(1), m.group(2)
        args2 = re.sub(r"index\s*=\s*\[([^\]]*?)\]", lambda mm: f"index=[" + re.sub(r"['\"]id_(?:loja|cliente)['\"]", f"SAFE_DIM_OF({var})", mm.group(1)) + "]", args)
        return f"{var}.pivot_table({args2})"
    def repl_pivot_columns(m: re.Match) -> str:
        var, args = m.group(1), m.group(2)
        args2 = re.sub(r"columns\s*=\s*\[([^\]]*?)\]", lambda mm: f"columns=[" + re.sub(r"['\"]id_(?:loja|cliente)['\"]", f"SAFE_DIM_OF({var})", mm.group(1)) + "]", args)
        return f"{var}.pivot_table({args2})"
    src = re.sub(r"([A-Za-z_][A-Za-z0-9_]*)\.pivot_table\(([^)]*?index\s*=\s*\[[^\]]*?\)[^)]*)\)", repl_pivot_index, src)
    src = re.sub(r"([A-Za-z_][A-Za-z0-9_]*)\.pivot_table\(([^)]*?columns\s*=\s*\[[^\]]*?\)[^)]*)\)", repl_pivot_columns, src)

    # dropna(subset=[...])
    def repl_dropna_subset(m: re.Match) -> str:
        var, inner = m.group(1), m.group(2)
        inner2 = re.sub(r"['\"]id_(?:loja|cliente)['\"]", f"SAFE_DIM_OF({var})", inner)
        return f"{var}.dropna(subset=[{inner2}])"
    src = re.sub(r"([A-Za-z_][A-Za-z0-9_]*)\.dropna\(\s*subset\s*=\s*\[([^\]]*?)\]\s*\)", repl_dropna_subset, src)

    # sort_values(by=[...])
    def repl_sort_values(m: re.Match) -> str:
        var, inner = m.group(1), m.group(2)
        inner2 = re.sub(r"['\"]id_(?:loja|cliente)['\"]", f"SAFE_DIM_OF({var})", inner)
        return f"{var}.sort_values(by=[{inner2}])"
    src = re.sub(r"([A-Za-z_][A-Za-z0-9_]*)\.sort_values\(\s*by\s*=\s*\[([^\]]*?)\]\s*\)", repl_sort_values, src)

    # set_index([...]) but avoid when immediately followed by .merge or joins
    def repl_set_index(m: re.Match) -> str:
        var, inner = m.group(1), m.group(2)
        inner2 = re.sub(r"['\"]id_(?:loja|cliente)['\"]", f"SAFE_DIM_OF({var})", inner)
        return f"{var}.set_index([{inner2}])"
    src = re.sub(r"([A-Za-z_][A-Za-z0-9_]*)\.set_index\(\s*\[([^\]]*?)\]\s*\)", repl_set_index, src)

    return src


def _cell_src(cell) -> str:
    try:
        return ''.join(cell.get('source', ''))
    except Exception:
        try:
            return ''.join(cell.source)
        except Exception:
            return ''


def ensure_helper(nb: nbf.NotebookNode) -> None:
    # ensure anti-id helper appears near top (after any style cell if present)
    helper_idx = None
    for idx, c in enumerate(nb.cells[:8]):
        if c.cell_type == 'code' and '[anti-id] helpers' in _cell_src(c):
            helper_idx = idx
            break
    if helper_idx is None:
        insert_pos = 1 if nb.cells and nb.cells[0].cell_type == 'code' else 0
        nb.cells.insert(insert_pos, nbf.v4.new_code_cell(ANTI_ID_HELPER))
    else:
        # If helper exists but outdated (missing BUSINESS_ENTITY_DIM), append minimal addendum just after it
        src = _cell_src(nb.cells[helper_idx])
        if 'def BUSINESS_ENTITY_DIM' not in src or 'def UNIQUE_KEYS' not in src:
            addendum = (
                "\n# [anti-id] addendum: funções auxiliares adicionais\n"
                "BUSINESS_ENTITY_PRIORITY = ['id_loja','id_cliente','id_vendedor','id_produto']\n"
                "def BUSINESS_ENTITY_DIM(df_like):\n"
                "    try:\n"
                "        cols = list(df_like.columns)\n"
                "    except Exception:\n"
                "        cols = []\n"
                "    for c in BUSINESS_ENTITY_PRIORITY:\n"
                "        if c in cols:\n"
                "            return c\n"
                "    return SAFE_DIM_OF(df_like)\n"
                "def UNIQUE_KEYS(*keys):\n"
                "    out = []\n"
                "    seen = set()\n"
                "    for k in keys:\n"
                "        if isinstance(k, (list, tuple)):\n"
                "            for x in k:\n"
                "                if x and x not in seen:\n"
                "                    seen.add(x); out.append(x)\n"
                "        else:\n"
                "            if k and k not in seen:\n"
                "                seen.add(k); out.append(k)\n"
                "    return out\n"
            )
            nb.cells.insert(helper_idx + 1, nbf.v4.new_code_cell(addendum))


def process_notebook(path: Path) -> Dict[str, Any]:
    nb = nbf.read(path, as_version=4)
    ensure_helper(nb)
    changes: List[Dict[str, Any]] = []
    detections: List[Dict[str, Any]] = []

    for i, cell in enumerate(nb.cells):
        if cell.cell_type != 'code':
            continue
        src = _cell_src(cell)

        # Detect risky filters for report
        for pat in DETECT_ONLY:
            if pat.search(src):
                detections.append({'notebook': str(path), 'cell': i, 'pattern': 'id_filter_usage'})

        original = src
        # First, apply simple regex replacements
        for pat, rep in PATTERNS:
            src = pat.sub(rep, src)
        # Second, make groupby(SAFE_DIM) object-aware: df.groupby(SAFE_DIM) -> df.groupby(SAFE_DIM_OF(df))
        # Avoid duplicating existing business dimension 'uf' in groupby lists
        src = re.sub(r"([A-Za-z_][A-Za-z0-9_]*)\.groupby\(\s*SAFE_DIM\s*\)", r"\1.groupby(SAFE_DIM_OF(\1))", src)
        src = re.sub(r"([A-Za-z_][A-Za-z0-9_]*)\.groupby\(\s*\[\s*'uf'\s*,\s*SAFE_DIM_OF\(\1\)\s*\]\s*\)", r"\1.groupby(['uf'])", src)
        src = re.sub(r"([A-Za-z_][A-Za-z0-9_]*)\.groupby\(\s*\[\s*SAFE_DIM_OF\(\1\)\s*,\s*'uf'\s*\]\s*\)", r"\1.groupby(['uf'])", src)
        # Object-aware pivot_table index/columns
        src = re.sub(r"([A-Za-z_][A-Za-z0-9_]*)\.pivot_table\(([^)]*?)index\s*=\s*SAFE_DIM", r"\1.pivot_table(\2index=SAFE_DIM_OF(\1)", src)
        src = re.sub(r"([A-Za-z_][A-Za-z0-9_]*)\.pivot_table\(([^)]*?)columns\s*=\s*SAFE_DIM", r"\1.pivot_table(\2columns=SAFE_DIM_OF(\1)", src)
        # Handle list contexts like groupby([...]), pivot_table(index=[...]), dropna(subset=[...]), sort_values(by=[...]), set_index([...])
        src = replace_id_in_list_context(src)
        # Also, if selecting column by [SAFE_DIM] after a groupby, use business entity dimension
        src = re.sub(r"([A-Za-z_][A-Za-z0-9_]*)\.groupby\(([^)]*)\)\s*\[\s*SAFE_DIM\s*\]", r"\1.groupby(\2)[BUSINESS_ENTITY_DIM(\1)]", src)
        # Seaborn: replace x=SAFE_DIM / hue=SAFE_DIM using data=<var> when present in the same call
        def _replace_seaborn_calls(code: str) -> str:
            # Heuristic: find patterns like sns.foo(... data=VAR ... x=SAFE_DIM ...)
            def _repl(match: re.Match) -> str:
                inner = match.group(2)
                # find data variable
                mdata = re.search(r"data\s*=\s*([A-Za-z_][A-Za-z0-9_]*)", inner)
                if not mdata:
                    return match.group(0)
                dvar = mdata.group(1)
                inner2 = re.sub(r"(\bx\s*=\s*)SAFE_DIM(\b)", rf"\\1SAFE_DIM_OF({dvar})\\2", inner)
                inner2 = re.sub(r"(\bhue\s*=\s*)SAFE_DIM(\b)", rf"\\1SAFE_DIM_OF({dvar})\\2", inner2)
                return f"sns.{match.group(1)}({inner2})"
            return re.sub(r"sns\.(\w+)\(([^)]*)\)", _repl, code)
        src = _replace_seaborn_calls(src)

        if src != original:
            cell.source = src
            changes.append({'notebook': str(path), 'cell': i})

    nbf.write(nb, path)
    return {'notebook': str(path), 'changes': changes, 'detections': detections}


def main():
    summary: Dict[str, Any] = {'processed': [], 'total_changes': 0, 'total_detections': 0}
    for p in NOTEBOOKS:
        if not p.exists():
            continue
        res = process_notebook(p)
        summary['processed'].append(res)
        summary['total_changes'] += len(res['changes'])
        summary['total_detections'] += len(res['detections'])

    out_dir = Path('reports') / '2025-08-15' / 'tables'
    out_dir.mkdir(parents=True, exist_ok=True)
    (out_dir / 'anti_id_summary.json').write_text(json.dumps(summary, ensure_ascii=False, indent=2), encoding='utf-8')
    print(json.dumps({'changes': summary['total_changes'], 'detections': summary['total_detections'], 'output': str(out_dir / 'anti_id_summary.json')}, ensure_ascii=False))


if __name__ == '__main__':
    main()

