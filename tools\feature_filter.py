from pathlib import Path
import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt

# Hardened anti-leakage feature filter
# - removes target and derived names
# - removes suspicious/identifier names
# - removes features with |corr| > 0.98 with the target
# - writes audit tables and correlation barplot (top20)

def suspicious_name(c: str, extra: list[str] | None = None) -> bool:
    cl = c.lower()
    patterns = [
        # direct target cues
        'valor', 'sq_valor', 'log_valor', 'valor_log', 'valor_log1p', 'boxcox',
        # synonyms/business terms that can be target-derived
        'revenue', 'sales', 'faturamento', 'ticket_medio', 'ticket medio', 'avg_valor', 'mean_valor', 'med_valor',
        # store-level aggregates or proxies
        'store_avg_', 'store_txn_', 'store_', 'loja_avg_', 'media_loja', 'freq_loja',
        # identifiers
        'cod_franqueado', 'cod_loja', 'codloja', 'cnpj', 'cpf', 'cliente_id', 'id_cliente', 'id_pedido', 'pedido', 'nota_fiscal', 'nf', 'ie_', 'im_',
        # generic ids
        '_id', 'id_', 'uuid', 'hash', 'chave_'
    ]
    if extra:
        patterns.extend([p.lower() for p in extra])
    return any(p in cl for p in patterns)


def compute_clean_features(df: pd.DataFrame, target: str, reports_dir: Path, corr_threshold: float = 0.98, suspicious_additional: list[str] | None = None):
    reports_dir.mkdir(parents=True, exist_ok=True)
    tables = reports_dir / 'tables'
    plots = reports_dir / 'plots' / 'eda'
    tables.mkdir(parents=True, exist_ok=True)
    plots.mkdir(parents=True, exist_ok=True)

    num_cols = [c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]
    original_num_count = len(num_cols)

    removed = []  # list of dicts: {'feature': c, 'reason': reason}

    # 1) name-based removal (target patterns and suspicious/identifiers)
    def is_target_derivative(c: str) -> bool:
        cl = c.lower(); t = target.lower()
        pats = [t, f'_{t}', f'{t}_', f'sq_{t}', f'{t}_boxcox', f'{t}_log', f'{t}_log1p', f'{t}_sqrt', f'log_{t}', f'boxcox_{t}']
        return any(p in cl for p in pats)

    keep = []
    for c in num_cols:
        if c == target:
            removed.append({'feature': c, 'reason': 'target'})
        elif is_target_derivative(c):
            removed.append({'feature': c, 'reason': 'target_derivative'})
        elif suspicious_name(c, suspicious_additional):
            removed.append({'feature': c, 'reason': 'identifier_or_suspicious'})
        else:
            keep.append(c)

    # 2) correlation filter |corr| > threshold with target
    # compute on keep only and rows with finite values
    df_sub = df[[target] + keep].copy()
    df_sub = df_sub.replace([np.inf, -np.inf], np.nan).dropna()
    corr = df_sub[keep].corrwith(df_sub[target]).abs().sort_values(ascending=False)
    high_corr = corr[corr > corr_threshold]
    for c, v in high_corr.items():
        removed.append({'feature': c, 'reason': f'corr>{corr_threshold}'})
    clean = [c for c in keep if c not in set(high_corr.index)]

    # 3) Write audit tables
    audit_removed = pd.DataFrame(removed)
    audit_removed.to_csv(tables / 'feature_audit_removed_features.csv', index=False)

    summary = pd.DataFrame([
        {
            'original_numeric_features': int(original_num_count),
            'removed_count': int(len(audit_removed)),
            'final_clean_count': int(len(clean))
        }
    ])
    summary.to_csv(tables / 'feature_audit_summary.csv', index=False)

    # 4) Correlation of top 20 remaining features
    if len(clean) > 0:
        df_corr = df_sub[[target] + [c for c in clean if c in df_sub.columns]].copy()
        corr_clean = df_corr[[c for c in clean if c in df_corr.columns]].corrwith(df_corr[target]).sort_values(key=np.abs, ascending=False)
        top20 = corr_clean.head(20)
        pd.DataFrame({'feature': top20.index, 'corr_with_target': top20.values}).to_csv(tables / 'feature_target_corr_top20.csv', index=False)
        # barplot
        plt.figure(figsize=(8, 6))
        sns.barplot(x=top20.values, y=top20.index, orient='h')
        plt.title('Top 20 correlações com o alvo (após anti-leakage)')
        plt.xlabel('Correlação (sinal mantido)'); plt.ylabel('Feature')
        plt.tight_layout(); plt.savefig(plots / 'feature_target_corr_top20.png', dpi=180); plt.close()

    return clean, audit_removed

