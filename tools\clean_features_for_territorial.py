from pathlib import Path
import sys
import re
import pandas as pd

# Ensure project root on sys.path for importing src.* when running from tools/
BASE = Path(__file__).resolve().parents[1]
sys.path.append(str(BASE))
from src.geo_utils import normalize_city_series

INP = BASE/'data'/'processed'/'features_engineered_regional.csv'
OUT = BASE/'data'/'processed'/'features_cleaned_pre_territorial.csv'

DROP_LEAKAGE = [
    'valor_boxcox',
    'sq_valor',
    'ratio_Preco_Custo_over_valor',
]

# Colunas constantes a remover
DROP_CONSTANTS = [
    'ratio_qtd_over_qtd_log1p',
    'Natureza_Operacao_VENDA DE MERCADORIA',
    'month',
]

# Ajustes de colinearidade
KEEP_PREFS = {
    'qtd': ['qtd', 'qtd_log1p', 'sq_qtd'],
    'day': ['day', 'day_boxcox'],
    'is_weekend': ['is_weekend_True', 'is_weekend_False'],
}

# Duplicatas de codificações: manter Grupo_Produto_* e remover GRUPO_CHILLI_*
DROP_PREFIXES = [
    'Dim_Produtos.GRUPO_CHILLI_',
]

# Consolidar *_nan vs *_was_missing: manter *_was_missing, remover *_nan
DROP_SUFFIXES = ['_nan']

# Para segurança, evitar KeyError ao remover

def drop_if_exists(df: pd.DataFrame, cols: list[str]) -> list[str]:
    removed = []
    for c in cols:
        if c in df.columns:
            df.drop(columns=[c], inplace=True)
            removed.append(c)
    return removed


def main():
    if not INP.exists():
        raise SystemExit(f'Input not found: {INP}')

    df = pd.read_csv(INP, low_memory=False)

    removed = []

    # 1) Remover leakage direto
    removed += drop_if_exists(df, DROP_LEAKAGE)

    # 1b) store_avg_valor* — excluir por ora (não há timestamp suficiente para refazer com janela t-1)
    removed += drop_if_exists(df, [c for c in df.columns if c in {'store_avg_valor', 'store_avg_valor_boxcox'}])

    # 1c) Remover constantes listadas
    removed += drop_if_exists(df, DROP_CONSTANTS)

    # 2) Reduzir colinearidade: manter preferidos e remover demais
    # qtd-group: manter 'qtd'
    if 'qtd' in df.columns:
        to_drop = [c for c in KEEP_PREFS['qtd'] if c != 'qtd']
        removed += drop_if_exists(df, to_drop)
    # day-group: manter 'day'
    if 'day' in df.columns:
        to_drop = [c for c in KEEP_PREFS['day'] if c != 'day']
        removed += drop_if_exists(df, to_drop)
    # is_weekend: manter 'is_weekend_True'
    if 'is_weekend_True' in df.columns:
        to_drop = [c for c in KEEP_PREFS['is_weekend'] if c != 'is_weekend_True']
        removed += drop_if_exists(df, to_drop)

    # 2b) Remover GRUPO_CHILLI_*
    drop_chilli = [c for c in df.columns if any(c.startswith(p) for p in DROP_PREFIXES)]
    removed += drop_if_exists(df, drop_chilli)

    # 2c) Remover *_nan e manter *_was_missing
    drop_nan_like = [c for c in df.columns if any(c.endswith(suf) for suf in DROP_SUFFIXES)]
    removed += drop_if_exists(df, drop_nan_like)

    # 3) Criar Cidade_std com normalização UPPERCASE sem acento
    if 'Cidade' in df.columns:
        df['Cidade_std'] = normalize_city_series(df['Cidade'])
    else:
        df['Cidade_std'] = ''

    # 4) Salvar dataset limpo
    OUT.parent.mkdir(parents=True, exist_ok=True)
    df.to_csv(OUT, index=False)

    # Sumário
    print('Saved cleaned dataset:', OUT)
    print('Removed columns count:', len(removed))
    if removed:
        print('Removed (sample up to 40):', sorted(removed)[:40])


if __name__ == '__main__':
    main()

