from pathlib import Path
import shutil
import numpy as np
import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt

from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import silhouette_score
from sklearn.cluster import KMeans

from feature_filter import compute_clean_features

BASE = Path('.')
REPORTS = BASE/'reports'/'2025-08-15'
PLOTS = REPORTS/'plots'/'presentation'/'slides'
TABLES = REPORTS/'tables'
DOCS = REPORTS/'docs'
for d in [PLOTS, TABLES, DOCS]:
    d.mkdir(parents=True, exist_ok=True)

DATA = BASE/'data'/'processed'/'features_engineered_regional.csv'
TARGET_COL = 'valor'

# ---------- Helpers ----------

def infer_business_group(col: str) -> str:
    c = col.lower()
    if 'preco' in c or 'custo' in c or 'desconto' in c:
        return 'Preço/Custo'
    if 'pdv' in c:
        return 'Canal/PDV'
    if c in {'uf','regiao','regiao_chilli'} or 'uf_' in c or 'reg_' in c:
        return 'Geográfico'
    if 'num_' in c or 'qt' in c:
        return 'Volume/Contagem'
    if 'nome_tabela' in c or 'tabela' in c:
        return 'Comercial/Política'
    return 'Outros'

def infer_business_desc(col: str) -> str:
    c = col
    if col.lower() == 'uf':
        return 'Unidade da Federação (estado) da loja/registro'
    if 'preco' in col.lower():
        return 'Métrica de preço (custo, venda, razão ou transformação log)'
    if 'custo' in col.lower():
        return 'Métrica de custo (nível, log ou razão)'
    if 'desconto' in col.lower():
        return 'Indicador percentual/valor de desconto aplicado'
    if 'pdv' in col.lower():
        return 'Tipologia de ponto de venda (ótica, quiosque, híbrido etc.)'
    if 'nome_tabela' in col.lower():
        return 'Identificador de tabela comercial vigente (ex.: outlet)'
    return 'Feature derivada dos dados transacionais e cadastrais'

# ---------- Load data ----------

df = pd.read_csv(DATA, low_memory=False)
if TARGET_COL not in df.columns:
    # fallback para última coluna numérica
    target = df.select_dtypes('number').columns[-1]
else:
    target = TARGET_COL

# ---------- Feature catalog ----------

clean_cols, audit_removed = compute_clean_features(df, target, REPORTS)
# incluir UF e REGIAO se existirem para documentação
doc_cols = list(dict.fromkeys((['UF','REGIAO_CHILLI'] if 'REGIAO_CHILLI' in df.columns else ['UF']) + clean_cols))

meta_rows = []
for col in doc_cols:
    if col not in df.columns:
        continue
    s = df[col]
    dtype = str(s.dtype)
    non_null = int(s.notna().sum())
    na_rate = float(1 - non_null/len(s))
    example = s.dropna().iloc[0] if non_null>0 else ''
    row = {
        'feature': col,
        'dtype': dtype,
        'non_null': non_null,
        'na_rate': round(na_rate, 4),
        'mean': float(np.nanmean(s)) if pd.api.types.is_numeric_dtype(s) else np.nan,
        'std': float(np.nanstd(s)) if pd.api.types.is_numeric_dtype(s) else np.nan,
        'min': float(np.nanmin(s)) if pd.api.types.is_numeric_dtype(s) else np.nan,
        'max': float(np.nanmax(s)) if pd.api.types.is_numeric_dtype(s) else np.nan,
        'group': infer_business_group(col),
        'business_desc': infer_business_desc(col)
    }
    meta_rows.append(row)

catalog = pd.DataFrame(meta_rows)
cat_path = TABLES/'feature_catalog.csv'
catalog.to_csv(cat_path, index=False)

# Relacionamentos entre variáveis (origem x derivadas) - heurística por sufixos/prefixos
rels = []
for col in catalog['feature']:
    if col.endswith('_log1p'):
        rels.append({'derivada': col, 'origem': col.replace('_log1p',''), 'tipo': 'Transformação log1p'})
    if 'ratio_' in col or 'over' in col:
        rels.append({'derivada': col, 'origem': 'ver colunas no nome', 'tipo': 'Razão/Percentual'})
    if col.startswith('UF_') or col.startswith('REG_'):
        rels.append({'derivada': col, 'origem': 'UF/REGIÃO', 'tipo': 'One‑hot geográfico'})
rels_df = pd.DataFrame(rels)
rels_path = TABLES/'feature_relationships.csv'
rels_df.to_csv(rels_path, index=False)

# ---------- Correlation heatmap (principais) ----------

num_cols = [c for c in clean_cols if pd.api.types.is_numeric_dtype(df[c])]
cor = df[num_cols].corrwith(pd.to_numeric(df[target], errors='coerce')).abs().sort_values(ascending=False)
top20 = cor.head(20).index.tolist() if len(cor)>0 else num_cols[:20]
plt.figure(figsize=(max(12, len(top20)*0.5), 7))
sns.heatmap(df[top20].corr(), cmap='vlag', center=0, square=False)
plt.title('Correlação entre features principais (Top 20)')
plt.tight_layout(); plt.savefig(PLOTS/'features_corr_heatmap_v1.png', dpi=320); plt.close()

# ---------- Feature importance (RF) ----------

X = df[clean_cols].select_dtypes('number').fillna(0.0)
sc = StandardScaler(with_mean=False)
Xsc = sc.fit_transform(X)
y = pd.to_numeric(df[target], errors='coerce').fillna(0.0)
rf = RandomForestRegressor(n_estimators=200, random_state=42, n_jobs=-1)
rf.fit(Xsc, y)
imp = pd.Series(rf.feature_importances_, index=X.columns).sort_values(ascending=False)
imp_top = imp.head(20)
plt.figure(figsize=(12,7))
imp_top[::-1].plot(kind='barh', color=sns.color_palette('Blues_r', len(imp_top)))
plt.xlabel('Importância (RF)'); plt.title('Importância de Features — Top 20 (RF)')
plt.tight_layout(); plt.savefig(PLOTS/'explain_feature_importance_rf_v1.png', dpi=320); plt.close()

# ---------- Comparação K=5 vs K=7 (territorial) ----------

# K=5 (originais)
old_assign_p = TABLES/'cluster_assignments.csv'
old_labels = None
if old_assign_p.exists():
    old_labels = pd.read_csv(old_assign_p)['cluster'].values

# K=7 (territorial)
new_assign_p = TABLES/'cluster_assignments_territorial.csv'
new_labels = None
if new_assign_p.exists():
    new_labels = pd.read_csv(new_assign_p)['cluster'].values

comp_path = TABLES/'clusters_k5_vs_k7_contingency.csv'
if old_labels is not None and new_labels is not None and len(old_labels)==len(new_labels):
    comp = pd.crosstab(pd.Series(old_labels, name='K5'), pd.Series(new_labels, name='K7'))
    comp.to_csv(comp_path)
    plt.figure(figsize=(10,7))
    sns.heatmap(comp, annot=True, fmt='d', cmap='YlGnBu')
    plt.title('K=5 vs K=7 — Matriz de correspondência (contagem)')
    plt.tight_layout(); plt.savefig(PLOTS/'clusters_k5_vs_k7_confusion_v1.png', dpi=320); plt.close()

# Silhouettes para comparação
sil_k5 = np.nan
sil_k7 = np.nan
try:
    if old_labels is not None:
        X_beh = df[clean_cols].select_dtypes('number').fillna(0.0)
        sil_k5 = float(silhouette_score(StandardScaler(with_mean=False).fit_transform(X_beh), old_labels))
except Exception:
    pass
try:
    if new_labels is not None:
        # Reconstituir X_final territorial (mesmo que em recluster_territorial)
        from recluster_territorial import UF_TO_REGION, UF_CENTROID  # reuse dictionaries
        uf_series = df['UF'].astype(str) if 'UF' in df.columns else pd.Series(['NA']*len(df))
        macro = uf_series.map(UF_TO_REGION).fillna('NA')
        macro_oh = pd.get_dummies(macro, prefix='REG', dtype=float)
        uf_counts = uf_series.value_counts()
        top_ufs = set(uf_counts.head(10).index.tolist())
        uf_capped = uf_series.where(uf_series.isin(top_ufs), other='OUTRAS')
        uf_oh = pd.get_dummies(uf_capped, prefix='UF', dtype=float)
        lonlat = uf_series.map(lambda u: UF_CENTROID.get(u, (np.nan, np.nan)))
        lon = pd.Series([x[0] for x in lonlat], name='lon')
        lat = pd.Series([x[1] for x in lonlat], name='lat')
        coords = pd.concat([lon, lat], axis=1).fillna(0.0)
        X_beh2 = df[clean_cols].select_dtypes('number').fillna(0.0)
        sc_macro = StandardScaler(with_mean=False).fit_transform(macro_oh)
        sc_uf = StandardScaler(with_mean=False).fit_transform(uf_oh)
        sc_coords = StandardScaler(with_mean=False).fit_transform(coords)
        sc_beh = StandardScaler(with_mean=False).fit_transform(X_beh2)
        from scipy import sparse
        def to_sparse(a):
            return a if sparse.issparse(a) else sparse.csr_matrix(a)
        W_MACRO, W_UF, W_COORDS, W_BEH = 3.0, 2.0, 1.5, 1.0
        X_final = sparse.hstack([
            to_sparse(sc_macro) * W_MACRO,
            to_sparse(sc_uf) * W_UF,
            to_sparse(sc_coords) * W_COORDS,
            to_sparse(sc_beh) * W_BEH
        ], format='csr')
        sil_k7 = float(silhouette_score(X_final, new_labels))
except Exception:
    pass

# ---------- Gráfico comparativo de qualidade ----------
plt.figure(figsize=(8,6))
vals = [v for v in [sil_k5, sil_k7] if not np.isnan(v)]
labels = ['K=5 (original)', 'K=7 (territorial)'][:len(vals)]
plt.bar(labels, vals, color=['#6baed6','#3182bd'])
for i, v in enumerate(vals):
    plt.text(i, v+0.01, f'{v:.3f}', ha='center')
plt.ylim(0, max(vals+[0.5]))
plt.title('Qualidade de clusters — Silhouette (maior é melhor)')
plt.tight_layout(); plt.savefig(PLOTS/'clusters_quality_comparison_v1.png', dpi=320); plt.close()

# ---------- Documentação executiva (Markdown) ----------

md = []
md.append('# Explicabilidade Executiva — Modelos Supervisionado e Clustering Territorial\n')
md.append('Este documento resume as variáveis usadas, a lógica de explicação dos modelos e a interpretação de resultados para decisões territoriais e de segmentação.\n')

md.append('\n## 1. Catálogo de Features\n')
md.append(f'- Catálogo salvo em: {cat_path.as_posix()} (colunas: feature, dtype, na_rate, estatísticas)')
md.append(f'\n- Relacionamentos salvo em: {rels_path.as_posix()}')

md.append('\n## 2. Análise de Explicabilidade\n')
md.append('- Importância de features (RF): imagens/explain_feature_importance_rf_v1.png')
md.append('- Correlação entre principais features: imagens/features_corr_heatmap_v1.png')
md.append('- Impacto geográfico no clustering territorial evidenciado por:')
md.append('  - clusters_territorial_distribution_v1.png, clusters_territorial_silhouette_box_v1.png, territorial_map_geo_clusters_v1.png')

md.append('\n## 3. Sumários dos Modelos\n')
md.append('- Supervisionado: use os gráficos já gerados (curvas de aprendizado, resíduos, predito vs real)')
md.append('- Clustering: comparação K=5 vs K=7 em clusters_quality_comparison_v1.png e matriz de correspondência clusters_k5_vs_k7_confusion_v1.png (se disponível)')

md.append('\n## 4. Métricas com interpretação executiva\n')
md.append('- Supervisionado: R², RMSE, MAE — maior R² e menores RMSE/MAE indicam previsões mais confiáveis para metas e orçamento.')
md.append(f'- Não supervisionado: Silhouette — K=5≈{sil_k5:.3f} vs K=7≈{sil_k7:.3f} (quanto maior, melhor coesão/separação).')

md.append('\n## 5. Recomendações\n')
md.append('- Usar os clusters territoriais (K=7) para “onde atuar” e o modelo supervisionado para “quanto atuar”.')
md.append('- Se necessário restringir para 5 segmentos, agrupar SC com PR/RS (Sul) e decidir se SP fica separado.')

md_content = '\n'.join(md)
md_path = DOCS/'explicabilidade_executiva.md'
md_path.write_text(md_content, encoding='utf-8')

# Optional: try to export PDF via pandoc if available and permitted (no installs)
try:
    if shutil.which('pandoc'):
        import subprocess
        pdf_path = DOCS/'explicabilidade_executiva.pdf'
        subprocess.run(['pandoc', str(md_path), '-o', str(pdf_path)], check=False)
except Exception:
    pass

print('Explainability docs built:')
print(' -', md_path)
print(' - images in', PLOTS)
print(' - tables in', TABLES)

