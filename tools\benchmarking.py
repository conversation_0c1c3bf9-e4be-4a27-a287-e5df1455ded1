from __future__ import annotations
from dataclasses import dataclass
from typing import Dict, <PERSON>, <PERSON><PERSON>, List, Optional
import numpy as np
import pandas as pd
from sklearn.model_selection import GridSearchCV, KFold
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error, make_scorer


def mape(y_true, y_pred):
    y_true = np.asarray(y_true); y_pred = np.asarray(y_pred)
    denom = np.clip(np.abs(y_true), 1e-8, None)
    return float(np.mean(np.abs((y_true - y_pred) / denom)))


def ci95(arr: np.ndarray) -> Tuple[float, float]:
    lo, hi = np.percentile(arr, [2.5, 97.5])
    return float(lo), float(hi)


@dataclass
class BenchmarkResult:
    model: str
    best_params: Dict[str, Any]
    r2_mean: float
    r2_lo: float
    r2_hi: float
    rmse_mean: float
    rmse_lo: float
    rmse_hi: float
    mae_mean: float
    mae_lo: float
    mae_hi: float
    mape_mean: float
    mape_lo: float
    mape_hi: float


def benchmark_models(
    X: pd.DataFrame,
    y: pd.Series,
    models: Dict[str, Any],
    param_grids: Dict[str, Dict[str, List[Any]]],
    cv_splits: int = 5,
    random_state: int = 42,
) -> pd.DataFrame:
    cv = KFold(n_splits=cv_splits, shuffle=True, random_state=random_state)
    results: List[BenchmarkResult] = []

    for name, estimator in models.items():
        grid = param_grids.get(name, {})
        if grid:
            gs = GridSearchCV(
                estimator,
                grid,
                cv=cv,
                scoring=make_scorer(r2_score),
                n_jobs=None,
                refit=True,
            )
            gs.fit(X, y)
            best_est = gs.best_estimator_
            best_params = gs.best_params_
        else:
            # Fit once on full data for predictions per fold
            best_est = estimator
            best_est.fit(X, y)
            best_params = {}

        # Manual CV estimates using the best_est's base class with refit each fold
        r2s, rmses, maes, mapes = [], [], [], []
        for tr_idx, te_idx in cv.split(X, y):
            Xtr, Xte = X.iloc[tr_idx], X.iloc[te_idx]
            ytr, yte = y.iloc[tr_idx], y.iloc[te_idx]
            est = best_est.__class__(**getattr(best_est, 'get_params', lambda: {})()) if hasattr(best_est, 'get_params') else best_est
            # If cloning fails, just reuse best_est
            try:
                est = best_est.__class__(**best_est.get_params())
            except Exception:
                est = best_est
            est.fit(Xtr, ytr)
            yp = est.predict(Xte)
            r2s.append(r2_score(yte, yp))
            rmses.append(np.sqrt(mean_squared_error(yte, yp)))
            maes.append(mean_absolute_error(yte, yp))
            mapes.append(mape(yte, yp))

        r2s, rmses, maes, mapes = map(np.asarray, (r2s, rmses, maes, mapes))
        r2_lo, r2_hi = ci95(r2s)
        rmse_lo, rmse_hi = ci95(rmses)
        mae_lo, mae_hi = ci95(maes)
        mape_lo, mape_hi = ci95(mapes)
        results.append(BenchmarkResult(
            model=name,
            best_params=best_params,
            r2_mean=float(r2s.mean()), r2_lo=r2_lo, r2_hi=r2_hi,
            rmse_mean=float(rmses.mean()), rmse_lo=rmse_lo, rmse_hi=rmse_hi,
            mae_mean=float(maes.mean()), mae_lo=mae_lo, mae_hi=mae_hi,
            mape_mean=float(mapes.mean()), mape_lo=mape_lo, mape_hi=mape_hi,
        ))

    import json
    df = pd.DataFrame([r.__dict__ for r in results])
    # normalize best_params as JSON string for CSV
    df['best_params'] = df['best_params'].apply(lambda d: json.dumps(d) if isinstance(d, dict) else str(d))
    return df

