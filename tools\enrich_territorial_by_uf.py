from pathlib import Path
import sys
import pandas as pd

BASE = Path(__file__).resolve().parents[1]
sys.path.append(str(BASE))

INPUT = BASE/'data'/'processed'/'features_cleaned_pre_territorial.csv'
UF_INDICATORS = BASE/'data'/'external'/'uf_indicators.csv'
OUT = BASE/'data'/'processed'/'features_enriched_uf.csv'

TEMPLATE_COLUMNS = [
    'UF',           # Sigla da UF (AC, AL, ...)
    'PIB_per_capita',
    'Populacao',
    'IDH',
    'Taxa_Desemprego',
]


def ensure_template():
    UF_INDICATORS.parent.mkdir(parents=True, exist_ok=True)
    if not UF_INDICATORS.exists():
        # Cria um template vazio com as colunas esperadas
        pd.DataFrame(columns=TEMPLATE_COLUMNS).to_csv(UF_INDICATORS, index=False)
        print('Template criado para indicadores UF em:', UF_INDICATORS)
        print('Preencha este arquivo com dados (IBGE/Atlas/PNAD) e reexecute o script.')
        return False
    else:
        return True


def main():
    if not INPUT.exists():
        raise SystemExit(f'Input não encontrado: {INPUT}')

    has_template = ensure_template()
    if not has_template:
        return

    df = pd.read_csv(INPUT)
    ind = pd.read_csv(UF_INDICATORS)

    # Sanitiza UF
    if 'UF' not in df.columns:
        raise SystemExit('Coluna UF não encontrada no dataset de entrada.')
    ind['UF'] = ind['UF'].astype(str).str.strip().str.upper()

    out = df.merge(ind, on='UF', how='left')
    OUT.parent.mkdir(parents=True, exist_ok=True)
    out.to_csv(OUT, index=False)
    print('Enriquecimento por UF concluído:', OUT)
    cov = 100.0 * (1 - out['PIB_per_capita'].isna().mean()) if 'PIB_per_capita' in out.columns else 0.0
    print(f'Cobertura (PIB_per_capita não-nulo): {cov:.2f}%')


if __name__ == '__main__':
    main()

