{"outliers_config": {"Preco_Custo": {"method": "iqr", "threshold": {"low": -64.78, "high": 197.3}, "justification": "p=0.000 ≤ 0.05; usar IQR (robusto)"}, "valor": {"method": "iqr", "threshold": {"low": -38999.5, "high": 87396.5}, "justification": "p=0.000 ≤ 0.05; usar IQR (robusto)"}, "Preco_Varejo": {"method": "iqr", "threshold": {"low": -389.995, "high": 873.965}, "justification": "p=0.000 ≤ 0.05; usar IQR (robusto)"}, "Frete": {"method": "iqr", "threshold": {"low": 0.0, "high": 0.0}, "justification": "p=0.000 ≤ 0.05; usar IQR (robusto)"}, "Num_Vale": {"method": "iqr", "threshold": {"low": 0.0, "high": 0.0}, "justification": "p=0.000 ≤ 0.05; usar IQR (robusto)"}, "Desconto": {"method": "iqr", "threshold": {"low": 0.0, "high": 0.0}, "justification": "p=0.000 ≤ 0.05; usar IQR (robusto)"}}, "distribution_transforms": {"qtd": {"distribution": "log1p"}, "Preco_Custo": {"distribution": "log1p"}, "valor": {"distribution": "boxcox", "lambda": 0.5919123663818529}, "Frete": {"distribution": "log1p"}, "Num_Vale": {"distribution": "log1p"}, "Desconto": {"distribution": "log1p"}, "day": {"distribution": "boxcox", "lambda": 0.2043370116827219}, "store_avg_valor": {"distribution": "boxcox", "lambda": 1.2424451056730355}, "store_txn_freq": {"distribution": "log1p"}}, "scaler_choice": {}}