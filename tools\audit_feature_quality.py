import argparse
from pathlib import Path
import numpy as np
import pandas as pd

# ------------------ Config via CLI ------------------
parser = argparse.ArgumentParser(description='Auditoria de qualidade de features (leakage/colinearidade)')
parser.add_argument('--input', default='data/processed/features_engineered_regional.csv')
parser.add_argument('--output_csv', default='reports/2025-08-15/tables/auditoria_feature_quality.csv')
parser.add_argument('--output_md', default='reports/2025-08-15/docs/auditoria_feature_quality.md')
parser.add_argument('--target', default=None, help='Nome do alvo (se None, tenta detectar)')
args = parser.parse_args()

BASE = Path('.')
INPUT = BASE/args.input
OUT_CSV = BASE/args.output_csv
OUT_MD = BASE/args.output_md
OUT_CSV.parent.mkdir(parents=True, exist_ok=True)
OUT_MD.parent.mkdir(parents=True, exist_ok=True)

if not INPUT.exists():
    raise FileNotFoundError(f'Arquivo de entrada não encontrado: {INPUT}')

# ------------------ Load ------------------
print('Auditing dataset:', INPUT)
df = pd.read_csv(INPUT, low_memory=False)

# ------------------ Target detection ------------------
def detect_target(columns, hint=None):
    cols_lower = {c.lower(): c for c in columns}
    if hint and hint in columns: return hint
    if hint and hint.lower() in cols_lower: return cols_lower[hint.lower()]
    for name in ['valor','target','label','y','receita','faturamento','sales','revenue']:
        if name in cols_lower: return cols_lower[name]
    # fallback substring
    for sub in ['valor','target','receita','fatur','sale','revenue']:
        for c in columns:
            if sub in c.lower():
                return c
    return None

TARGET = detect_target(df.columns.tolist(), args.target)
if TARGET is None:
    raise RuntimeError('Não foi possível identificar a coluna alvo (ex.: valor).')

# ------------------ Helpers ------------------
def basic_stats(series: pd.Series):
    if pd.api.types.is_numeric_dtype(series):
        d = series.describe(percentiles=[0.25,0.5,0.75])
        return dict(mean=float(d.get('mean', np.nan)), std=float(d.get('std', np.nan)),
                    min=float(d.get('min', np.nan)), p25=float(d.get('25%', np.nan)),
                    median=float(d.get('50%', np.nan)), p75=float(d.get('75%', np.nan)),
                    max=float(d.get('max', np.nan)))
    return dict(mean=np.nan, std=np.nan, min=np.nan, p25=np.nan, median=np.nan, p75=np.nan, max=np.nan)

def safe_corr(a, b):
    try:
        sa = pd.to_numeric(a, errors='coerce')
        sb = pd.to_numeric(b, errors='coerce')
        if sa.nunique()<2 or sb.nunique()<2: return np.nan
        return float(sa.corr(sb))
    except Exception:
        return np.nan

# ------------------ Correlations ------------------
num_cols = [c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]
corr_with_target = {}
if TARGET in df.columns:
    ty = pd.to_numeric(df[TARGET], errors='coerce')
    for c in num_cols:
        if c==TARGET: continue
        corr_with_target[c] = abs(safe_corr(df[c], ty))

# Colinearidade (pares com |corr| > 0.95)
very_high_pairs = []
if len(num_cols) > 1:
    sample = df[num_cols]
    if len(sample) > 6000:
        sample = sample.sample(6000, random_state=42)
    C = sample.corr().abs()
    cols = C.columns.tolist()
    for i in range(len(cols)):
        for j in range(i+1, len(cols)):
            v = C.iloc[i, j]
            if pd.notna(v) and v > 0.95:
                very_high_pairs.append((cols[i], cols[j], float(v)))
very_high_pairs.sort(key=lambda t: t[2], reverse=True)

# ------------------ Leakage heuristics ------------------
KW_SUS = {'valor','receita','fatur','lucro','margem','desconto','discount','revenue','sales','total','amount','gross','net','price','preco'}
MARKERS = {'boxcox','log','log1p','ratio','perc','pct','rate','prop','share','sq_','_over_'}

def leakage_flags(col: str):
    name = col.lower()
    suspicious_name = any(k in name for k in KW_SUS) or any(m in name for m in MARKERS)
    derived = (TARGET.lower() in name) or ('_over_' in name and ('valor' in name or TARGET.lower() in name))
    return suspicious_name, derived

# ------------------ Per-column metrics ------------------
records = []
N = len(df)
for col in df.columns:
    s = df[col]
    dtype = str(s.dtype)
    n_null = int(s.isna().sum())
    null_rate = float(n_null / N) if N else np.nan
    nunique = int(s.nunique(dropna=True))
    unique_ratio = float(nunique / N) if N else np.nan
    is_constant = (nunique <= 1)
    is_id = unique_ratio >= 0.99  # quase único por linha
    is_high_card = unique_ratio > 0.5
    stats = basic_stats(s)
    corr_abs = float(corr_with_target.get(col, np.nan))
    susp_name, derived = leakage_flags(col)
    risk = 0; reasons = []
    if not np.isnan(corr_abs) and corr_abs > 0.8:
        risk += 2; reasons.append(f'|corr(target)|={corr_abs:.3f} > 0.8')
    if susp_name:
        risk += 1; reasons.append('nome suspeito/transformação')
    if derived:
        risk += 2; reasons.append('possível derivação direta do alvo')
    records.append({
        'column': col,
        'dtype': dtype,
        'mean': stats['mean'], 'std': stats['std'], 'min': stats['min'], 'p25': stats['p25'],
        'median': stats['median'], 'p75': stats['p75'], 'max': stats['max'],
        'n_nulls': n_null, 'null_rate': null_rate,
        'unique_count': nunique, 'unique_ratio': unique_ratio,
        'is_constant': is_constant, 'is_id': is_id, 'is_high_cardinality': is_high_card,
        'corr_abs_target': corr_abs,
        'suspicious_name_flag': susp_name,
        'derived_from_target_flag': derived,
        'leakage_risk_score': risk,
        'leakage_reasons': '; '.join(dict.fromkeys(reasons)),
    })

metrics = pd.DataFrame.from_records(records)
metrics.sort_values(['leakage_risk_score','corr_abs_target'], ascending=[False, False]).to_csv(OUT_CSV, index=False)

# ------------------ Markdown report ------------------
from datetime import datetime
n_rows, n_cols = df.shape
n_numeric = len(num_cols)
n_object = df.select_dtypes(include=['object']).shape[1]
n_constants = int(metrics['is_constant'].sum())
n_ids = int(metrics['is_id'].sum())
n_high_card = int(metrics['is_high_cardinality'].sum())
n_high_null = int((metrics['null_rate'] > 0.2).sum())
leakage_rank = metrics.sort_values(['leakage_risk_score','corr_abs_target'], ascending=[False, False])
top_leak = leakage_rank.head(10)[['column','leakage_risk_score','corr_abs_target','leakage_reasons']]

summary = [
    f"- Linhas: {n_rows}",
    f"- Colunas: {n_cols} (numéricas: {n_numeric}, categóricas: {n_object}, outras: {n_cols - n_numeric - n_object})",
    f"- Alvo (target): {TARGET}",
    f"- Constantes: {n_constants}",
    f"- Possíveis IDs: {n_ids}",
    f"- Alta cardinalidade (>50%): {n_high_card}",
    f"- Alta taxa de nulos (>20%): {n_high_null}",
    f"- Pares colineares (|corr|>0.95): {len(very_high_pairs)}",
    f"- Tabela completa: {OUT_CSV.as_posix()}",
]

recs = [
    'Remover colunas constantes.',
    'Revisar e possivelmente excluir colunas marcadas como possíveis IDs (alta cardinalidade única).',
    'Para colunas de alta cardinalidade, considerar target/frequency encoding com validação robusta.',
    'Tratar colunas com alta taxa de nulos (>20%): descartar, imputar ou criar flag explícita.',
    'Verificar features com alto risco de leakage; evitar transformações diretas do alvo (ex.: *_boxcox, ratio_*_over_valor).',
    'Para pares colineares (|corr|>0.95), manter apenas uma feature do par ou aplicar regularização.',
    'Validar impacto das decisões via CV antes do enriquecimento territorial.',
]

md = []
md.append(f"# Auditoria de Qualidade de Features — {datetime.now().strftime('%Y-%m-%d %H:%M')}")
md.append('\n## Resumo executivo')
md.extend(summary)
md.append('\n## Top 10 features com maior risco de leakage')
if top_leak.empty:
    md.append('Nenhuma feature com risco de leakage detectada pelas heurísticas.')
else:
    md.append('| Feature | Score | |corr(target)| | Razões |')
    md.append('|---|---:|---:|---|')
    for _, r in top_leak.iterrows():
        md.append(f"| {r['column']} | {int(r['leakage_risk_score'])} | {'' if pd.isna(r['corr_abs_target']) else f'{r['corr_abs_target']:.3f}'} | {r['leakage_reasons']} |")
md.append('\n## Pares colineares (|corr| > 0.95)')
if not very_high_pairs:
    md.append('Nenhum par com colinearidade alta encontrado acima do limiar.')
else:
    md.append('| Feature A | Feature B | |corr| |')
    md.append('|---|---|---:|')
    for a,b,c in very_high_pairs[:100]:
        md.append(f'| {a} | {b} | {c:.4f} |')
md.append('\n## Recomendações específicas')
for r in recs: md.append(f'- {r}')
md.append('\n## Estatísticas gerais do dataset')
md.append('| Métrica | Valor |')
md.append('|---|---:|')
md.append(f'| Linhas | {n_rows} |')
md.append(f'| Colunas | {n_cols} |')
md.append(f'| Numéricas | {n_numeric} |')
md.append(f'| Categóricas | {n_object} |')
md.append(f'| Alvo | {TARGET} |')

Path(OUT_MD).write_text('\n'.join(md), encoding='utf-8')
print('OK - CSV:', OUT_CSV)
print('OK - MD: ', OUT_MD)
print('Target:', TARGET)
print('High collinear pairs:', len(very_high_pairs))

