# Auditoria de Qualidade de Features — 2025-09-12 09:19

## Resumo executivo
- Linhas: 35616
- Colunas: 187 (numéricas: 183, categóricas: 4, outras: 0)
- Alvo (target): valor
- Constantes: 3
- Possíveis IDs: 0
- Alta cardinalidade (>50%): 0
- Alta taxa de nulos (>20%): 0
- Pares colineares (|corr|>0.95): 127
- Tabela completa: reports/2025-08-15/tables/auditoria_feature_quality.csv

## Top 10 features com maior risco de leakage
| Feature | Score | |corr(target)| | Razões |
|---|---:|---:|---|
| valor_boxcox | 5 | 0.981 | |corr(target)|=0.981 > 0.8; nome suspeito/transformação; possível derivação direta do alvo |
| store_avg_valor | 3 | 0.442 | nome suspeito/transformação; possível derivação direta do alvo |
| store_avg_valor_boxcox | 3 | 0.441 | nome suspeito/transformação; possível derivação direta do alvo |
| sq_valor | 3 | 0.021 | nome suspeito/transformação; possível derivação direta do alvo |
| ratio_Preco_Custo_over_valor | 3 | 0.006 | nome suspeito/transformação; possível derivação direta do alvo |
| valor | 3 |  | nome suspeito/transformação; possível derivação direta do alvo |
| Preco_Custo_log1p | 1 | 0.796 | nome suspeito/transformação |
| ratio_Preco_Custo_over_Preco_Custo_log1p | 1 | 0.771 | nome suspeito/transformação |
| Preco_Custo | 1 | 0.710 | nome suspeito/transformação |
| store_txn_freq_log1p | 1 | 0.188 | nome suspeito/transformação |

## Pares colineares (|corr| > 0.95)
| Feature A | Feature B | |corr| |
|---|---|---:|
| is_weekend_False | is_weekend_True | 1.0000 |
| qtd | qtd_log1p | 1.0000 |
| qtd | sq_qtd | 1.0000 |
| qtd_log1p | sq_qtd | 1.0000 |
| Dim_Cliente.Cep_Cliente_was_missing | Dim_Cliente.Sexo_was_missing | 1.0000 |
| Dim_Cliente.Cep_Cliente_was_missing | Dim_Cliente.Data_Cadastro_was_missing | 1.0000 |
| Dim_Cliente.Cep_Cliente_was_missing | Dim_Cliente.Data_Nascimento_was_missing | 1.0000 |
| Dim_Cliente.Cep_Cliente_was_missing | Dim_Cliente.Ativo_was_missing | 1.0000 |
| Dim_Cliente.Cep_Cliente_was_missing | Dim_Cliente.Estado_Civil_was_missing | 1.0000 |
| Dim_Cliente.Cep_Cliente_was_missing | Dim_Cliente.Regiao_Cliente_was_missing | 1.0000 |
| Dim_Cliente.Cep_Cliente_was_missing | Dim_Cliente.Sexo_nan | 1.0000 |
| Dim_Cliente.Cep_Cliente_was_missing | Dim_Cliente.Ativo_nan | 1.0000 |
| Dim_Cliente.Cep_Cliente_was_missing | Dim_Cliente.Estado_Civil_nan | 1.0000 |
| cidade_was_missing | cidade_freq_was_missing | 1.0000 |
| uf_was_missing | Dim_Cliente.Pais_was_missing | 1.0000 |
| uf_was_missing | Dim_Cliente.Pais_nan | 1.0000 |
| Dim_Cliente.Pais_was_missing | Dim_Cliente.Pais_nan | 1.0000 |
| Dim_Cliente.Sexo_was_missing | Dim_Cliente.Data_Cadastro_was_missing | 1.0000 |
| Dim_Cliente.Sexo_was_missing | Dim_Cliente.Data_Nascimento_was_missing | 1.0000 |
| Dim_Cliente.Sexo_was_missing | Dim_Cliente.Ativo_was_missing | 1.0000 |
| Dim_Cliente.Sexo_was_missing | Dim_Cliente.Estado_Civil_was_missing | 1.0000 |
| Dim_Cliente.Sexo_was_missing | Dim_Cliente.Regiao_Cliente_was_missing | 1.0000 |
| Dim_Cliente.Sexo_was_missing | Dim_Cliente.Sexo_nan | 1.0000 |
| Dim_Cliente.Sexo_was_missing | Dim_Cliente.Ativo_nan | 1.0000 |
| Dim_Cliente.Sexo_was_missing | Dim_Cliente.Estado_Civil_nan | 1.0000 |
| Dim_Cliente.Data_Cadastro_was_missing | Dim_Cliente.Data_Nascimento_was_missing | 1.0000 |
| Dim_Cliente.Data_Cadastro_was_missing | Dim_Cliente.Ativo_was_missing | 1.0000 |
| Dim_Cliente.Data_Cadastro_was_missing | Dim_Cliente.Estado_Civil_was_missing | 1.0000 |
| Dim_Cliente.Data_Cadastro_was_missing | Dim_Cliente.Regiao_Cliente_was_missing | 1.0000 |
| Dim_Cliente.Data_Cadastro_was_missing | Dim_Cliente.Sexo_nan | 1.0000 |
| Dim_Cliente.Data_Cadastro_was_missing | Dim_Cliente.Ativo_nan | 1.0000 |
| Dim_Cliente.Data_Cadastro_was_missing | Dim_Cliente.Estado_Civil_nan | 1.0000 |
| Dim_Cliente.Data_Nascimento_was_missing | Dim_Cliente.Ativo_was_missing | 1.0000 |
| Dim_Cliente.Data_Nascimento_was_missing | Dim_Cliente.Estado_Civil_was_missing | 1.0000 |
| Dim_Cliente.Data_Nascimento_was_missing | Dim_Cliente.Regiao_Cliente_was_missing | 1.0000 |
| Dim_Cliente.Data_Nascimento_was_missing | Dim_Cliente.Sexo_nan | 1.0000 |
| Dim_Cliente.Data_Nascimento_was_missing | Dim_Cliente.Ativo_nan | 1.0000 |
| Dim_Cliente.Data_Nascimento_was_missing | Dim_Cliente.Estado_Civil_nan | 1.0000 |
| Dim_Cliente.Ativo_was_missing | Dim_Cliente.Estado_Civil_was_missing | 1.0000 |
| Dim_Cliente.Ativo_was_missing | Dim_Cliente.Regiao_Cliente_was_missing | 1.0000 |
| Dim_Cliente.Ativo_was_missing | Dim_Cliente.Sexo_nan | 1.0000 |
| Dim_Cliente.Ativo_was_missing | Dim_Cliente.Ativo_nan | 1.0000 |
| Dim_Cliente.Ativo_was_missing | Dim_Cliente.Estado_Civil_nan | 1.0000 |
| Dim_Cliente.Estado_Civil_was_missing | Dim_Cliente.Regiao_Cliente_was_missing | 1.0000 |
| Dim_Cliente.Estado_Civil_was_missing | Dim_Cliente.Sexo_nan | 1.0000 |
| Dim_Cliente.Estado_Civil_was_missing | Dim_Cliente.Ativo_nan | 1.0000 |
| Dim_Cliente.Estado_Civil_was_missing | Dim_Cliente.Estado_Civil_nan | 1.0000 |
| Dim_Cliente.Regiao_Cliente_was_missing | Dim_Cliente.Sexo_nan | 1.0000 |
| Dim_Cliente.Regiao_Cliente_was_missing | Dim_Cliente.Ativo_nan | 1.0000 |
| Dim_Cliente.Regiao_Cliente_was_missing | Dim_Cliente.Estado_Civil_nan | 1.0000 |
| Tipo_PDV_was_missing | Dim_Lojas.ID_SAP_was_missing | 1.0000 |
| Dim_Lojas.Cod_Franqueado_was_missing | Dim_Lojas.CANAL_VENDA_NÃO IDENTIFICADO | 1.0000 |
| Dim_Produtos.Cor2_was_missing | Dim_Produtos.Material2_was_missing | 1.0000 |
| Dim_Produtos.Cor2_was_missing | Dim_Produtos.Sexo_was_missing | 1.0000 |
| Dim_Produtos.Cor2_was_missing | Dim_Produtos.Griffe_was_missing | 1.0000 |
| Dim_Produtos.Material2_was_missing | Dim_Produtos.Sexo_was_missing | 1.0000 |
| Dim_Produtos.Material2_was_missing | Dim_Produtos.Griffe_was_missing | 1.0000 |
| Dim_Produtos.Segmentacao_was_missing | Dim_Produtos.Segmentacao_nan | 1.0000 |
| Dim_Produtos.Shape_was_missing | Dim_Produtos.Shape_nan | 1.0000 |
| Dim_Produtos.Sexo_was_missing | Dim_Produtos.Griffe_was_missing | 1.0000 |
| Dim_Cliente.Sexo_nan | Dim_Cliente.Ativo_nan | 1.0000 |
| Dim_Cliente.Sexo_nan | Dim_Cliente.Estado_Civil_nan | 1.0000 |
| Dim_Cliente.Ativo_nan | Dim_Cliente.Estado_Civil_nan | 1.0000 |
| Dim_Produtos.Grupo_Produto_ACESSORIOS | Dim_Produtos.GRUPO_CHILLI_ACESSORIOS | 1.0000 |
| Dim_Produtos.Grupo_Produto_BAG | Dim_Produtos.GRUPO_CHILLI_BAG | 1.0000 |
| Dim_Produtos.Grupo_Produto_RELOGIOS | Dim_Produtos.GRUPO_CHILLI_RELOGIOS | 1.0000 |
| Dim_Cliente.Cep_Cliente_was_missing | Dim_Cliente.Ativo_S | 1.0000 |
| Dim_Cliente.Sexo_was_missing | Dim_Cliente.Ativo_S | 1.0000 |
| Dim_Cliente.Data_Cadastro_was_missing | Dim_Cliente.Ativo_S | 1.0000 |
| Dim_Cliente.Data_Nascimento_was_missing | Dim_Cliente.Ativo_S | 1.0000 |
| Dim_Cliente.Ativo_was_missing | Dim_Cliente.Ativo_S | 1.0000 |
| Dim_Cliente.Estado_Civil_was_missing | Dim_Cliente.Ativo_S | 1.0000 |
| Dim_Cliente.Regiao_Cliente_was_missing | Dim_Cliente.Ativo_S | 1.0000 |
| Dim_Cliente.Sexo_nan | Dim_Cliente.Ativo_S | 1.0000 |
| Dim_Cliente.Ativo_S | Dim_Cliente.Ativo_nan | 1.0000 |
| Dim_Cliente.Ativo_S | Dim_Cliente.Estado_Civil_nan | 1.0000 |
| store_avg_valor | store_avg_valor_boxcox | 0.9969 |
| Dim_Lojas.Cod_Franqueado | __orig_code_by_rank__ | 0.9959 |
| Dim_Produtos.Shape_was_missing | Dim_Produtos.Segmentacao_nan | 0.9896 |
| Dim_Produtos.Segmentacao_was_missing | Dim_Produtos.Shape_was_missing | 0.9896 |
| Dim_Produtos.Segmentacao_was_missing | Dim_Produtos.Shape_nan | 0.9896 |
| Dim_Produtos.Segmentacao_nan | Dim_Produtos.Shape_nan | 0.9896 |
| Dim_Produtos.Shape_MULTI | Dim_Produtos.GRUPO_CHILLI_MULTI | 0.9871 |
| day | day_boxcox | 0.9856 |
| Dim_Produtos.Segmentacao_KIDS | Dim_Produtos.Shape_KIDS | 0.9828 |
| valor | valor_boxcox | 0.9802 |
| Preco_Custo | ratio_Preco_Custo_over_Preco_Custo_log1p | 0.9752 |
| ratio_qtd_over_Dim_Produtos.Grupo_Produto_LENTES VISTA | ratio_qtd_over_Dim_Produtos.GRUPO_CHILLI_LENTES VISTA | 0.9718 |
| Dim_Cliente.Cep_Cliente_was_missing | uf_was_missing | 0.9715 |
| Dim_Cliente.Cep_Cliente_was_missing | Dim_Cliente.Pais_was_missing | 0.9715 |
| Dim_Cliente.Cep_Cliente_was_missing | Dim_Cliente.Pais_nan | 0.9715 |
| uf_was_missing | Dim_Cliente.Sexo_was_missing | 0.9715 |
| uf_was_missing | Dim_Cliente.Data_Cadastro_was_missing | 0.9715 |
| uf_was_missing | Dim_Cliente.Data_Nascimento_was_missing | 0.9715 |
| uf_was_missing | Dim_Cliente.Ativo_was_missing | 0.9715 |
| uf_was_missing | Dim_Cliente.Estado_Civil_was_missing | 0.9715 |
| uf_was_missing | Dim_Cliente.Regiao_Cliente_was_missing | 0.9715 |
| uf_was_missing | Dim_Cliente.Sexo_nan | 0.9715 |
| uf_was_missing | Dim_Cliente.Ativo_nan | 0.9715 |
| uf_was_missing | Dim_Cliente.Estado_Civil_nan | 0.9715 |

## Recomendações específicas
- Remover colunas constantes.
- Revisar e possivelmente excluir colunas marcadas como possíveis IDs (alta cardinalidade única).
- Para colunas de alta cardinalidade, considerar target/frequency encoding com validação robusta.
- Tratar colunas com alta taxa de nulos (>20%): descartar, imputar ou criar flag explícita.
- Verificar features com alto risco de leakage; evitar transformações diretas do alvo (ex.: *_boxcox, ratio_*_over_valor).
- Para pares colineares (|corr|>0.95), manter apenas uma feature do par ou aplicar regularização.
- Validar impacto das decisões via CV antes do enriquecimento territorial.

## Estatísticas gerais do dataset
| Métrica | Valor |
|---|---:|
| Linhas | 35616 |
| Colunas | 187 |
| Numéricas | 183 |
| Categóricas | 4 |
| Alvo | valor |