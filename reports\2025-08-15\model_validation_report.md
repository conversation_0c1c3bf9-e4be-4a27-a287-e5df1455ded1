# Relatório de Validação de Modelos — 2025‑08‑15

## 1. Correções de vazamento aplicadas

- Médias móveis agora usam `shift(1)` antes do `rolling()`.
- Remoção do uso de `qtd` do mesmo dia; inclusão de `qtd_lag_1` e `qtd_lag_7`.
- Split temporal por loja (holdout adaptativo: 20% últimos dias, máx. 14, mín. 1).
- Remoção de `id_loja` como numérico; uso de target encoding seguro dentro das dobras.

## 2. Metodologia antes/depois

- Antes: possíveis splits aleatórios; risco de vazamento em rolling e uso de `qtd` corrente.
- Depois: validação temporal (TimeSeriesSplit) e holdout final por loja; engenharia de atributos com salvaguardas anti‑leakage.

## 3. Resultados atuais (resumo)

- Tabela `reports/2025-08-15/tables/model_benchmark.csv` consolidada com WAPE/sMAPE/MAE/RMSE (CV e Holdout).
- Baselines por loja no holdout em `baseline_holdout_metrics.csv`.
- Plots comparativos e uplift em `plots/model_comparison/`.

## 4. Recomendações para produção

- Expandir horizonte histórico para robustez estatística.
- Monitoramento contínuo: WAPE semanal por loja/região vs baseline sazonal; alertas se degradação > 5 p.p.
- Re‑treino mensal com janela móvel; lock de hiperparâmetros aprovado em Change Advisory Board.
- Versionar datasets de treino e artefatos (MLflow ou DVC).

## 5. Limitações e próximos passos

- Curto histórico reduz robustez de CV e holdout.
- Ausência de séries longas pode limitar sazonalidade e efeitos calendário complexos.
- Próximos: encoder alvo K‑fold com purga de janelas, métricas executivas completas, dashboards operacionais.

