# Dicionário de Dados

Este dicionário descreve as colunas canônicas utilizadas no projeto após o processo de padronização e limpeza.

| Coluna | Tipo | Descrição | Regras / Observações |
|--------|------|-----------|----------------------|
| data | date | Data da venda (YYYY-MM-DD) | Não nulo. Convertida de formatos diversos. |
| id_loja | string | Identificador único da loja / ponto de venda | Não nulo. Normalizado para string. |
| valor | int | Valor de receita em centavos | Original em decimal/moeda → multiplicado por 100 e convertido para inteiro. |
| qtd | float | Quantidade de itens vendidos | Zeros possíveis; nulos preenchidos com 0 quando fizer sentido. |
| id_produto | string | Identificador do produto | Pode conter nulos; usado em análises de Pareto se presente. |
| uf | string (2) | Unidade Federativa (UF) | Uppercase; validado contra lista de UFs brasileiras. |
| cidade | string | Nome da cidade | Capitalização tipo título. |
| Tipo_PDV | string | Tipo / classe do ponto de venda | Opcional; usado em boxplots e heatmaps. |

## Colunas derivadas
| Coluna | Fonte | Descrição |
|--------|-------|-----------|
| receita_diaria | valor | Soma de valor por (data, id_loja) |
| idade_da_loja | data de primeira aparição | Diferença em dias entre data corrente e primeira data da loja |
| dow | data | Dia da semana (0=Seg ... 6=Dom) |
| semana_do_ano | data | Semana ISO (1-53) |
| mes | data | Mês numérico (1-12) |
| ano | data | Ano (YYYY) |
| is_fim_semana | data | Flag (1 se sábado ou domingo) |

## Regras de Validação
1. Presença obrigatória: data, id_loja, valor.
2. Datas fora do intervalo plausível (ex: <2015 ou > hoje + 7d) são descartadas.
3. Valor negativo: mantido somente se representar estornos; caso contrário, filtrado e logado.
4. Duplicatas exatas (todas as colunas) são removidas mantendo a primeira ocorrência.
5. Normalização monetária: arredondar para centavos antes de converter para int.

## Lista de UFs válidas
AC, AL, AM, AP, BA, CE, DF, ES, GO, MA, MG, MS, MT, PA, PB, PE, PI, PR, RJ, RN, RO, RR, RS, SC, SE, SP, TO.

---
Atualize este arquivo sempre que novas colunas forem padronizadas ou derivadas.
