import nbformat as nbf
from pathlib import Path

md = lambda t: nbf.v4.new_markdown_cell(t)
code = lambda t: nbf.v4.new_code_cell(t)

BASE = Path(__file__).resolve().parents[1]
NB_PATH = BASE / 'notebooks' / 'model_diagnostics_and_fixes.ipynb'

nb = nbf.v4.new_notebook()

nb.cells.append(md(
    "# Diagnóstico e Correção de Modelos - ChilliAnalyzer MVP\n\n"
    "Este notebook investiga o problema do R² negativo extremo no modelo Linear Regression "
    "e implementa correções técnicas para melhorar a performance de todos os modelos."
))

# 1. Setup e carregamento
nb.cells.append(md('## 1. Setup e Carregamento de Dados'))
nb.cells.append(code(
    "from pathlib import Path\n"
    "import pandas as pd, numpy as np\n"
    "import matplotlib.pyplot as plt\n"
    "import seaborn as sns\n"
    "from sklearn.model_selection import KFold, cross_validate, learning_curve\n"
    "from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler\n"
    "from sklearn.linear_model import LinearRegression, Ridge, Lasso\n"
    "from sklearn.ensemble import RandomForestRegressor\n"
    "from sklearn.svm import SVR\n"
    "from sklearn.neural_network import MLPRegressor\n"
    "from sklearn.pipeline import Pipeline\n"
    "from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error\n"
    "from scipy import stats\n"
    "from scipy.stats import boxcox\n"
    "import time\n"
    "import warnings; warnings.filterwarnings('ignore')\n"
    "# Configurações de visualização\n"
    "plt.style.use('seaborn-v0_8')\n"
    "plt.rcParams['figure.dpi'] = 300\n"
    "plt.rcParams['savefig.dpi'] = 300\n"
    "plt.rcParams['font.size'] = 10\n"
    "BASE = Path('.')\n"
    "if not (BASE/'data'/'processed'/'features_engineered.csv').exists(): BASE = Path('..')\n"
    "DATA = BASE/'data'/'processed'/'features_engineered.csv'\n"
    "REPORTS = BASE/'reports'/'2025-08-15'/'model_comparison'\n"
    "REPORTS.mkdir(parents=True, exist_ok=True)\n"
    "print('Carregando dados de:', DATA)\n"
))

nb.cells.append(code(
    "# Carregar e preparar dados\n"
    "df = pd.read_csv(DATA, low_memory=False)\n"
    "print('Dataset shape:', df.shape)\n"
    "# Target e features\n"
    "TARGET = 'valor' if 'valor' in df.columns else df.columns[-1]\n"
    "y = df[TARGET]\n"
    "# Selecionar apenas features numéricas, excluindo IDs\n"
    "id_patterns = ['id_', '_id', 'id']\n"
    "numeric_cols = df.select_dtypes(include=['number']).columns\n"
    "feature_cols = [c for c in numeric_cols if c != TARGET and \n"
    "               not any(pattern in c.lower() for pattern in id_patterns)]\n"
    "X = df[feature_cols]\n"
    "print(f'Target: {TARGET}, Features: {len(feature_cols)}')\n"
    "print(f'Target stats: min={y.min():.2f}, max={y.max():.2f}, mean={y.mean():.2f}, std={y.std():.2f}')\n"
    "# Downsample para execução estável\n"
    "MAX_SAMPLES = 5000\n"
    "if len(df) > MAX_SAMPLES:\n"
    "    idx = np.random.RandomState(42).choice(len(df), MAX_SAMPLES, replace=False)\n"
    "    X, y = X.iloc[idx].reset_index(drop=True), y.iloc[idx].reset_index(drop=True)\n"
    "    print(f'Downsampled to {len(X)} samples')\n"
))

# 2. Diagnóstico do problema
nb.cells.append(md('## 2. Diagnóstico do Problema - Linear Regression'))
nb.cells.append(code(
    "# 2.1 Análise de outliers extremos\n"
    "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n"
    "# Target distribution\n"
    "axes[0,0].hist(y, bins=50, alpha=0.7, edgecolor='black')\n"
    "axes[0,0].set_title('Distribuição do Target')\n"
    "axes[0,0].set_xlabel(TARGET)\n"
    "# Boxplot do target\n"
    "axes[0,1].boxplot(y)\n"
    "axes[0,1].set_title('Boxplot do Target (Outliers)')\n"
    "axes[0,1].set_ylabel(TARGET)\n"
    "# Q-Q plot para normalidade\n"
    "stats.probplot(y, dist='norm', plot=axes[1,0])\n"
    "axes[1,0].set_title('Q-Q Plot - Normalidade do Target')\n"
    "# Correlação com features principais\n"
    "corr_with_target = X.corrwith(y).abs().sort_values(ascending=False)\n"
    "top_features = corr_with_target.head(10)\n"
    "axes[1,1].barh(range(len(top_features)), top_features.values)\n"
    "axes[1,1].set_yticks(range(len(top_features)))\n"
    "axes[1,1].set_yticklabels(top_features.index, fontsize=8)\n"
    "axes[1,1].set_title('Top 10 Correlações com Target')\n"
    "axes[1,1].set_xlabel('|Correlação|')\n"
    "plt.tight_layout()\n"
    "plt.savefig(REPORTS/'target_analysis_diagnostics.png', bbox_inches='tight')\n"
    "plt.show()\n"
    "print('Outliers extremos no target:')\n"
    "q1, q3 = y.quantile([0.25, 0.75])\n"
    "iqr = q3 - q1\n"
    "outliers = y[(y < q1 - 3*iqr) | (y > q3 + 3*iqr)]\n"
    "print(f'Outliers (±3 IQR): {len(outliers)} ({len(outliers)/len(y)*100:.1f}%)')\n"
))

nb.cells.append(code(
    "# 2.2 Análise de multicolinearidade\n"
    "from sklearn.preprocessing import StandardScaler\n"
    "# Selecionar top features para análise\n"
    "top_10_features = corr_with_target.head(10).index.tolist()\n"
    "X_top = X[top_10_features].fillna(X[top_10_features].median())\n"
    "# Matriz de correlação entre features\n"
    "corr_matrix = X_top.corr()\n"
    "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n"
    "# Heatmap de correlação\n"
    "sns.heatmap(corr_matrix, annot=True, cmap='RdBu_r', center=0, \n"
    "           square=True, ax=ax1, fmt='.2f')\n"
    "ax1.set_title('Matriz de Correlação - Top Features')\n"
    "# Identificar pares altamente correlacionados\n"
    "high_corr_pairs = []\n"
    "for i in range(len(corr_matrix.columns)):\n"
    "    for j in range(i+1, len(corr_matrix.columns)):\n"
    "        corr_val = abs(corr_matrix.iloc[i, j])\n"
    "        if corr_val > 0.8:\n"
    "            high_corr_pairs.append((corr_matrix.columns[i], corr_matrix.columns[j], corr_val))\n"
    "# VIF (Variance Inflation Factor) simulado\n"
    "from sklearn.linear_model import LinearRegression\n"
    "vif_data = []\n"
    "X_scaled = StandardScaler().fit_transform(X_top)\n"
    "for i, feature in enumerate(X_top.columns):\n"
    "    X_temp = np.delete(X_scaled, i, axis=1)\n"
    "    y_temp = X_scaled[:, i]\n"
    "    try:\n"
    "        r2 = LinearRegression().fit(X_temp, y_temp).score(X_temp, y_temp)\n"
    "        vif = 1 / (1 - r2) if r2 < 0.999 else 999\n"
    "    except:\n"
    "        vif = 999\n"
    "    vif_data.append(vif)\n"
    "# Plot VIF\n"
    "ax2.barh(range(len(X_top.columns)), vif_data)\n"
    "ax2.set_yticks(range(len(X_top.columns)))\n"
    "ax2.set_yticklabels(X_top.columns, fontsize=8)\n"
    "ax2.set_xlabel('VIF (Variance Inflation Factor)')\n"
    "ax2.set_title('Multicolinearidade (VIF > 5 = problema)')\n"
    "ax2.axvline(x=5, color='red', linestyle='--', alpha=0.7, label='Limite VIF=5')\n"
    "ax2.axvline(x=10, color='darkred', linestyle='--', alpha=0.7, label='Limite VIF=10')\n"
    "ax2.legend()\n"
    "plt.tight_layout()\n"
    "plt.savefig(REPORTS/'multicollinearity_analysis.png', bbox_inches='tight')\n"
    "plt.show()\n"
    "print(f'Pares altamente correlacionados (|r| > 0.8): {len(high_corr_pairs)}')\n"
    "for pair in high_corr_pairs[:5]:\n"
    "    print(f'  {pair[0]} <-> {pair[1]}: {pair[2]:.3f}')\n"
    "print(f'Features com VIF > 10: {sum(1 for vif in vif_data if vif > 10)}')\n"
))

# 3. Correções técnicas
nb.cells.append(md('## 3. Correções Técnicas e Modelos Melhorados'))
nb.cells.append(code(
    "# 3.1 Preparação de dados com diferentes estratégias\n"
    "def prepare_data_strategies(X, y):\n"
    "    strategies = {}\n"
    "    \n"
    "    # Estratégia 1: Dados originais (fillna apenas)\n"
    "    X_orig = X.fillna(X.median())\n"
    "    strategies['original'] = (X_orig, y, 'Dados originais')\n"
    "    \n"
    "    # Estratégia 2: Remoção de outliers extremos\n"
    "    q1, q3 = y.quantile([0.25, 0.75])\n"
    "    iqr = q3 - q1\n"
    "    mask_outliers = (y >= q1 - 2*iqr) & (y <= q3 + 2*iqr)\n"
    "    X_no_outliers = X_orig[mask_outliers]\n"
    "    y_no_outliers = y[mask_outliers]\n"
    "    strategies['no_outliers'] = (X_no_outliers, y_no_outliers, 'Sem outliers extremos')\n"
    "    \n"
    "    # Estratégia 3: Transformação log do target\n"
    "    y_log = np.log1p(y - y.min() + 1)  # Garantir valores positivos\n"
    "    strategies['log_target'] = (X_orig, y_log, 'Target log-transformado')\n"
    "    \n"
    "    # Estratégia 4: Seleção de features (top correlacionadas)\n"
    "    top_features = corr_with_target.head(15).index.tolist()\n"
    "    X_selected = X_orig[top_features]\n"
    "    strategies['selected_features'] = (X_selected, y, 'Features selecionadas')\n"
    "    \n"
    "    return strategies\n"
    "\n"
    "data_strategies = prepare_data_strategies(X, y)\n"
    "print('Estratégias de preparação de dados:')\n"
    "for name, (X_prep, y_prep, desc) in data_strategies.items():\n"
    "    print(f'  {name}: {desc} - X: {X_prep.shape}, y: {y_prep.shape}')\n"
))

nb.cells.append(code(
    "# 3.2 Modelos com diferentes configurações\n"
    "def get_models():\n"
    "    models = {\n"
    "        'LinReg_Standard': Pipeline([\n"
    "            ('scaler', StandardScaler()),\n"
    "            ('model', LinearRegression())\n"
    "        ]),\n"
    "        'LinReg_Robust': Pipeline([\n"
    "            ('scaler', RobustScaler()),\n"
    "            ('model', LinearRegression())\n"
    "        ]),\n"
    "        'Ridge': Pipeline([\n"
    "            ('scaler', StandardScaler()),\n"
    "            ('model', Ridge(alpha=1.0, random_state=42))\n"
    "        ]),\n"
    "        'Lasso': Pipeline([\n"
    "            ('scaler', StandardScaler()),\n"
    "            ('model', Lasso(alpha=0.1, random_state=42, max_iter=2000))\n"
    "        ]),\n"
    "        'RandomForest': RandomForestRegressor(\n"
    "            n_estimators=200, max_depth=10, random_state=42, n_jobs=1\n"
    "        ),\n"
    "        'SVM': Pipeline([\n"
    "            ('scaler', StandardScaler()),\n"
    "            ('model', SVR(kernel='rbf', C=1.0, gamma='scale'))\n"
    "        ]),\n"
    "        'MLP': Pipeline([\n"
    "            ('scaler', StandardScaler()),\n"
    "            ('model', MLPRegressor(hidden_layer_sizes=(64,32), max_iter=500, random_state=42))\n"
    "        ])\n"
    "    }\n"
    "    return models\n"
    "\n"
    "models = get_models()\n"
    "print(f'Modelos configurados: {list(models.keys())}')\n"
))

# 4. Avaliação completa
nb.cells.append(md('## 4. Avaliação Completa com Métricas Expandidas'))
nb.cells.append(code(
    "# 4.1 Função para calcular métricas expandidas\n"
    "def calculate_expanded_metrics(y_true, y_pred):\n"
    "    metrics = {}\n"
    "    metrics['rmse'] = np.sqrt(mean_squared_error(y_true, y_pred))\n"
    "    metrics['mae'] = mean_absolute_error(y_true, y_pred)\n"
    "    metrics['r2'] = r2_score(y_true, y_pred)\n"
    "    \n"
    "    # MAPE (tratando divisão por zero)\n"
    "    mask = y_true != 0\n"
    "    if mask.sum() > 0:\n"
    "        mape = np.mean(np.abs((y_true[mask] - y_pred[mask]) / y_true[mask])) * 100\n"
    "        metrics['mape'] = mape\n"
    "    else:\n"
    "        metrics['mape'] = np.inf\n"
    "    \n"
    "    return metrics\n"
    "\n"
    "# 4.2 Avaliação com validação cruzada\n"
    "def evaluate_models_comprehensive(models, X, y, cv_folds=5):\n"
    "    results = []\n"
    "    cv = KFold(n_splits=cv_folds, shuffle=True, random_state=42)\n"
    "    \n"
    "    for model_name, model in models.items():\n"
    "        print(f'Avaliando {model_name}...')\n"
    "        \n"
    "        # Métricas por fold\n"
    "        fold_metrics = {'rmse': [], 'mae': [], 'r2': [], 'mape': [], 'train_time': []}\n"
    "        \n"
    "        for train_idx, val_idx in cv.split(X):\n"
    "            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]\n"
    "            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]\n"
    "            \n"
    "            # Treinamento com tempo\n"
    "            start_time = time.time()\n"
    "            try:\n"
    "                model.fit(X_train, y_train)\n"
    "                train_time = time.time() - start_time\n"
    "                \n"
    "                # Predição\n"
    "                y_pred = model.predict(X_val)\n"
    "                \n"
    "                # Métricas\n"
    "                metrics = calculate_expanded_metrics(y_val, y_pred)\n"
    "                for metric, value in metrics.items():\n"
    "                    fold_metrics[metric].append(value)\n"
    "                fold_metrics['train_time'].append(train_time)\n"
    "                \n"
    "            except Exception as e:\n"
    "                print(f'  Erro no {model_name}: {e}')\n"
    "                # Preencher com valores ruins\n"
    "                fold_metrics['rmse'].append(1e6)\n"
    "                fold_metrics['mae'].append(1e6)\n"
    "                fold_metrics['r2'].append(-1e6)\n"
    "                fold_metrics['mape'].append(1e6)\n"
    "                fold_metrics['train_time'].append(0)\n"
    "        \n"
    "        # Compilar resultados\n"
    "        result = {'model': model_name}\n"
    "        for metric, values in fold_metrics.items():\n"
    "            if len(values) > 0:\n"
    "                result[f'{metric}_mean'] = np.mean(values)\n"
    "                result[f'{metric}_std'] = np.std(values)\n"
    "                result[f'{metric}_values'] = values\n"
    "            else:\n"
    "                result[f'{metric}_mean'] = np.nan\n"
    "                result[f'{metric}_std'] = np.nan\n"
    "                result[f'{metric}_values'] = []\n"
    "        \n"
    "        results.append(result)\n"
    "    \n"
    "    return results\n"
    "\n"
    "# Avaliar com dados originais\n"
    "X_eval, y_eval, _ = data_strategies['original']\n"
    "results = evaluate_models_comprehensive(models, X_eval, y_eval)\n"
    "print('\\nAvaliação concluída!')\n"
))

# 5. Visualizações de performance obrigatórias
nb.cells.append(md('## 5. Visualizações de Performance Obrigatórias'))
nb.cells.append(code(
    "# 5.1 Gráfico de barras comparativo com todas as métricas\n"
    "results_df = pd.DataFrame(results)\n"
    "metrics_to_plot = ['rmse_mean', 'mae_mean', 'r2_mean', 'mape_mean']\n"
    "available_metrics = [m for m in metrics_to_plot if m in results_df.columns]\n"
    "\n"
    "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n"
    "axes = axes.ravel()\n"
    "\n"
    "for i, metric in enumerate(available_metrics):\n"
    "    ax = axes[i]\n"
    "    \n"
    "    # Filtrar valores válidos\n"
    "    valid_data = results_df[results_df[metric].notna() & np.isfinite(results_df[metric])]\n"
    "    \n"
    "    if len(valid_data) > 0:\n"
    "        # Limitar valores extremos para visualização\n"
    "        values = valid_data[metric].copy()\n"
    "        if metric in ['rmse_mean', 'mae_mean', 'mape_mean']:\n"
    "            # Limitar valores muito altos\n"
    "            cap = np.percentile(values[np.isfinite(values)], 95)\n"
    "            values = np.clip(values, 0, cap * 2)\n"
    "        \n"
    "        bars = ax.bar(valid_data['model'], values, \n"
    "                     color=sns.color_palette('husl', len(valid_data)))\n"
    "        \n"
    "        # Título e labels\n"
    "        metric_name = metric.replace('_mean', '').upper()\n"
    "        ax.set_title(f'{metric_name} por Modelo', fontweight='bold')\n"
    "        ax.set_ylabel(metric_name)\n"
    "        ax.tick_params(axis='x', rotation=45)\n"
    "        \n"
    "        # Anotar valores nas barras\n"
    "        for bar, val in zip(bars, values):\n"
    "            height = bar.get_height()\n"
    "            if np.isfinite(height) and height != 0:\n"
    "                ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,\n"
    "                       f'{val:.3f}', ha='center', va='bottom', fontsize=8)\n"
    "        \n"
    "        ax.grid(True, alpha=0.3)\n"
    "    else:\n"
    "        ax.text(0.5, 0.5, f'Dados não disponíveis\\npara {metric}', \n"
    "               ha='center', va='center', transform=ax.transAxes)\n"
    "\n"
    "plt.tight_layout()\n"
    "plt.savefig(REPORTS/'comprehensive_metrics_comparison.png', bbox_inches='tight')\n"
    "plt.show()\n"
))

nb.cells.append(code(
    "# 5.2 Boxplots das métricas por fold\n"
    "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n"
    "axes = axes.ravel()\n"
    "\n"
    "metrics_for_box = ['rmse', 'mae', 'r2', 'mape']\n"
    "\n"
    "for i, metric in enumerate(metrics_for_box):\n"
    "    ax = axes[i]\n"
    "    \n"
    "    # Preparar dados para boxplot\n"
    "    box_data = []\n"
    "    labels = []\n"
    "    \n"
    "    for result in results:\n"
    "        values_key = f'{metric}_values'\n"
    "        if values_key in result and len(result[values_key]) > 0:\n"
    "            values = np.array(result[values_key])\n"
    "            # Filtrar valores válidos\n"
    "            valid_values = values[np.isfinite(values)]\n"
    "            if len(valid_values) > 0:\n"
    "                # Limitar valores extremos\n"
    "                if metric in ['rmse', 'mae', 'mape']:\n"
    "                    cap = np.percentile(valid_values, 95) * 2\n"
    "                    valid_values = np.clip(valid_values, 0, cap)\n"
    "                box_data.append(valid_values)\n"
    "                labels.append(result['model'])\n"
    "    \n"
    "    if len(box_data) > 0:\n"
    "        bp = ax.boxplot(box_data, labels=labels, patch_artist=True)\n"
    "        \n"
    "        # Colorir boxplots\n"
    "        colors = sns.color_palette('husl', len(box_data))\n"
    "        for patch, color in zip(bp['boxes'], colors):\n"
    "            patch.set_facecolor(color)\n"
    "            patch.set_alpha(0.7)\n"
    "        \n"
    "        ax.set_title(f'Distribuição {metric.upper()} por Fold', fontweight='bold')\n"
    "        ax.set_ylabel(metric.upper())\n"
    "        ax.tick_params(axis='x', rotation=45)\n"
    "        ax.grid(True, alpha=0.3)\n"
    "    else:\n"
    "        ax.text(0.5, 0.5, f'Dados não disponíveis\\npara {metric}', \n"
    "               ha='center', va='center', transform=ax.transAxes)\n"
    "\n"
    "plt.tight_layout()\n"
    "plt.savefig(REPORTS/'metrics_distribution_boxplots.png', bbox_inches='tight')\n"
    "plt.show()\n"
))

# 6. Scatter plots e análise de resíduos
nb.cells.append(md('## 6. Scatter Plots e Análise de Resíduos'))
nb.cells.append(code(
    "# 6.1 Treinar modelos para análise de resíduos\n"
    "trained_models = {}\n"
    "predictions = {}\n"
    "\n"
    "# Usar subset para análise visual\n"
    "X_sample = X_eval.sample(min(1000, len(X_eval)), random_state=42)\n"
    "y_sample = y_eval.loc[X_sample.index]\n"
    "\n"
    "for model_name, model in models.items():\n"
    "    try:\n"
    "        model.fit(X_sample, y_sample)\n"
    "        y_pred = model.predict(X_sample)\n"
    "        trained_models[model_name] = model\n"
    "        predictions[model_name] = y_pred\n"
    "    except Exception as e:\n"
    "        print(f'Erro ao treinar {model_name}: {e}')\n"
    "\n"
    "print(f'Modelos treinados para análise: {list(trained_models.keys())}')\n"
))

nb.cells.append(code(
    "# 6.2 Scatter plots: predito vs real\n"
    "n_models = len(predictions)\n"
    "if n_models > 0:\n"
    "    cols = min(3, n_models)\n"
    "    rows = (n_models + cols - 1) // cols\n"
    "    \n"
    "    fig, axes = plt.subplots(rows, cols, figsize=(5*cols, 4*rows))\n"
    "    if n_models == 1:\n"
    "        axes = [axes]\n"
    "    elif rows == 1:\n"
    "        axes = axes.reshape(1, -1)\n"
    "    axes = axes.ravel()\n"
    "    \n"
    "    for i, (model_name, y_pred) in enumerate(predictions.items()):\n"
    "        ax = axes[i]\n"
    "        \n"
    "        # Scatter plot\n"
    "        ax.scatter(y_sample, y_pred, alpha=0.6, s=20)\n"
    "        \n"
    "        # Linha de referência (predito = real)\n"
    "        min_val = min(y_sample.min(), y_pred.min())\n"
    "        max_val = max(y_sample.max(), y_pred.max())\n"
    "        ax.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, label='Perfeito')\n"
    "        \n"
    "        # Métricas no gráfico\n"
    "        r2 = r2_score(y_sample, y_pred)\n"
    "        rmse = np.sqrt(mean_squared_error(y_sample, y_pred))\n"
    "        ax.text(0.05, 0.95, f'R² = {r2:.3f}\\nRMSE = {rmse:.1f}', \n"
    "               transform=ax.transAxes, verticalalignment='top',\n"
    "               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n"
    "        \n"
    "        ax.set_xlabel('Valores Reais')\n"
    "        ax.set_ylabel('Valores Preditos')\n"
    "        ax.set_title(f'{model_name}\\nPredito vs Real', fontweight='bold')\n"
    "        ax.legend()\n"
    "        ax.grid(True, alpha=0.3)\n"
    "    \n"
    "    # Ocultar axes extras\n"
    "    for i in range(n_models, len(axes)):\n"
    "        axes[i].set_visible(False)\n"
    "    \n"
    "    plt.tight_layout()\n"
    "    plt.savefig(REPORTS/'predicted_vs_actual_scatter.png', bbox_inches='tight')\n"
    "    plt.show()\n"
))

nb.cells.append(code(
    "# 6.3 Análise de resíduos\n"
    "if len(predictions) > 0:\n"
    "    cols = min(3, len(predictions))\n"
    "    rows = (len(predictions) + cols - 1) // cols\n"
    "    \n"
    "    fig, axes = plt.subplots(rows, cols, figsize=(5*cols, 4*rows))\n"
    "    if len(predictions) == 1:\n"
    "        axes = [axes]\n"
    "    elif rows == 1:\n"
    "        axes = axes.reshape(1, -1)\n"
    "    axes = axes.ravel()\n"
    "    \n"
    "    for i, (model_name, y_pred) in enumerate(predictions.items()):\n"
    "        ax = axes[i]\n"
    "        \n"
    "        # Calcular resíduos\n"
    "        residuals = y_sample - y_pred\n"
    "        \n"
    "        # Scatter plot dos resíduos\n"
    "        ax.scatter(y_pred, residuals, alpha=0.6, s=20)\n"
    "        ax.axhline(y=0, color='r', linestyle='--', alpha=0.8)\n"
    "        \n"
    "        # Estatísticas dos resíduos\n"
    "        mean_residual = np.mean(residuals)\n"
    "        std_residual = np.std(residuals)\n"
    "        ax.text(0.05, 0.95, f'Média = {mean_residual:.2f}\\nStd = {std_residual:.2f}', \n"
    "               transform=ax.transAxes, verticalalignment='top',\n"
    "               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n"
    "        \n"
    "        ax.set_xlabel('Valores Preditos')\n"
    "        ax.set_ylabel('Resíduos (Real - Predito)')\n"
    "        ax.set_title(f'{model_name}\\nAnálise de Resíduos', fontweight='bold')\n"
    "        ax.grid(True, alpha=0.3)\n"
    "    \n"
    "    # Ocultar axes extras\n"
    "    for i in range(len(predictions), len(axes)):\n"
    "        axes[i].set_visible(False)\n"
    "    \n"
    "    plt.tight_layout()\n"
    "    plt.savefig(REPORTS/'residuals_analysis.png', bbox_inches='tight')\n"
    "    plt.show()\n"
))

# 7. Relatório final e recomendações
nb.cells.append(md('## 7. Relatório Final e Recomendações'))
nb.cells.append(code(
    "# 7.1 Tabela comparativa consolidada\n"
    "def create_comprehensive_report(results):\n"
    "    report_data = []\n"
    "    \n"
    "    for result in results:\n"
    "        model_name = result['model']\n"
    "        \n"
    "        # Compilar métricas com intervalos de confiança\n"
    "        row = {\n"
    "            'Modelo': model_name,\n"
    "            'RMSE': f\"{result.get('rmse_mean', np.nan):.3f} ± {result.get('rmse_std', 0):.3f}\",\n"
    "            'MAE': f\"{result.get('mae_mean', np.nan):.3f} ± {result.get('mae_std', 0):.3f}\",\n"
    "            'R²': f\"{result.get('r2_mean', np.nan):.3f} ± {result.get('r2_std', 0):.3f}\",\n"
    "            'MAPE (%)': f\"{result.get('mape_mean', np.nan):.1f} ± {result.get('mape_std', 0):.1f}\",\n"
    "            'Tempo (s)': f\"{result.get('train_time_mean', 0):.3f}\"\n"
    "        }\n"
    "        \n"
    "        # Classificação de performance\n"
    "        r2_mean = result.get('r2_mean', -999)\n"
    "        if r2_mean > 0.9:\n"
    "            performance = 'Excelente'\n"
    "        elif r2_mean > 0.7:\n"
    "            performance = 'Boa'\n"
    "        elif r2_mean > 0.5:\n"
    "            performance = 'Regular'\n"
    "        elif r2_mean > 0:\n"
    "            performance = 'Ruim'\n"
    "        else:\n"
    "            performance = 'Inadequada'\n"
    "        \n"
    "        row['Performance'] = performance\n"
    "        \n"
    "        # Recomendação de uso\n"
    "        if 'LinReg' in model_name:\n"
    "            if r2_mean > 0.5:\n"
    "                recommendation = 'Interpretabilidade'\n"
    "            else:\n"
    "                recommendation = 'Não recomendado'\n"
    "        elif 'Ridge' in model_name or 'Lasso' in model_name:\n"
    "            recommendation = 'Regularização/Interpretabilidade'\n"
    "        elif 'RandomForest' in model_name:\n"
    "            recommendation = 'Uso geral/Robustez'\n"
    "        elif 'SVM' in model_name:\n"
    "            recommendation = 'Dados médios/Não-linear'\n"
    "        elif 'MLP' in model_name:\n"
    "            recommendation = 'Padrões complexos'\n"
    "        else:\n"
    "            recommendation = 'Específico'\n"
    "        \n"
    "        row['Recomendação'] = recommendation\n"
    "        report_data.append(row)\n"
    "    \n"
    "    return pd.DataFrame(report_data)\n"
    "\n"
    "report_df = create_comprehensive_report(results)\n"
    "print('=== RELATÓRIO COMPARATIVO DE MODELOS ===')\n"
    "print(report_df.to_string(index=False))\n"
    "\n"
    "# Salvar relatório\n"
    "report_df.to_csv(REPORTS/'comprehensive_model_report.csv', index=False)\n"
    "print(f'\\nRelatório salvo em: {REPORTS}/comprehensive_model_report.csv')\n"
))

nb.cells.append(code(
    "# 7.2 Ranking e recomendações finais\n"
    "print('\\n=== DIAGNÓSTICO DO PROBLEMA LINEAR REGRESSION ===')\n"
    "linreg_results = [r for r in results if 'LinReg' in r['model']]\n"
    "if linreg_results:\n"
    "    for result in linreg_results:\n"
    "        model_name = result['model']\n"
    "        r2_mean = result.get('r2_mean', np.nan)\n"
    "        print(f'{model_name}: R² = {r2_mean:.3f}')\n"
    "        \n"
    "        if r2_mean < -100:\n"
    "            print(f'  ❌ PROBLEMA CRÍTICO: R² extremamente negativo')\n"
    "            print(f'  🔍 Possíveis causas:')\n"
    "            print(f'     - Outliers extremos no target')\n"
    "            print(f'     - Multicolinearidade severa')\n"
    "            print(f'     - Features inadequadas para relação linear')\n"
    "            print(f'     - Problemas de escala')\n"
    "        elif r2_mean < 0:\n"
    "            print(f'  ⚠️  Performance inadequada: modelo pior que média')\n"
    "        else:\n"
    "            print(f'  ✅ Performance aceitável após correções')\n"
    "\n"
    "print('\\n=== RECOMENDAÇÕES FINAIS ===')\n"
    "# Ordenar por R² para ranking\n"
    "results_sorted = sorted(results, key=lambda x: x.get('r2_mean', -999), reverse=True)\n"
    "\n"
    "print('🏆 RANKING DE MODELOS (por R²):')\n"
    "for i, result in enumerate(results_sorted[:5], 1):\n"
    "    model_name = result['model']\n"
    "    r2_mean = result.get('r2_mean', np.nan)\n"
    "    rmse_mean = result.get('rmse_mean', np.nan)\n"
    "    print(f'{i}. {model_name}: R² = {r2_mean:.3f}, RMSE = {rmse_mean:.3f}')\n"
    "\n"
    "print('\\n🎯 RECOMENDAÇÕES POR CENÁRIO:')\n"
    "print('• Máxima Performance: Random Forest ou melhor modelo do ranking')\n"
    "print('• Interpretabilidade: Ridge/Lasso (se R² > 0.5)')\n"
    "print('• Dados Pequenos: SVM ou Ridge')\n"
    "print('• Tempo Limitado: Linear Regression (se corrigido)')\n"
    "print('• Padrões Complexos: MLP ou Random Forest')\n"
    "\n"
    "print('\\n📊 PRÓXIMOS PASSOS:')\n"
    "print('1. Implementar correções específicas para Linear Regression')\n"
    "print('2. Testar com diferentes estratégias de dados')\n"
    "print('3. Considerar ensemble methods')\n"
    "print('4. Validar em conjunto de teste independente')\n"
))

# Write notebook
NB_PATH.parent.mkdir(parents=True, exist_ok=True)
with open(NB_PATH, 'w', encoding='utf-8') as f:
    nbf.write(nb, f)
print('Notebook built at', NB_PATH)
