{"audited": ["notebooks\\chilli_beans_analysis.ipynb", "notebooks\\model_comparison_colab.ipynb", "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "notebooks\\preprocessamento\\eda_geografico.ipynb", "notebooks\\preprocessamento\\eda_indice.ipynb"], "issues_count": 103, "issues": [{"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 12, "type": "needs-interpretation", "suggestion": "Adicionar célula markdown após o gráfico com interpretação dos resultados."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 13, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 13, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 13, "type": "missing-legend", "suggestion": "Incluir legenda clara quando houver múltiplas séries (plt.legend ou ax.legend)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 15, "type": "needs-interpretation", "suggestion": "Adicionar célula markdown após o gráfico com interpretação dos resultados."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 16, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 16, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 16, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 19, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 19, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 19, "type": "needs-interpretation", "suggestion": "Adicionar célula markdown após o gráfico com interpretação dos resultados."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 20, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 25, "type": "needs-interpretation", "suggestion": "Adicionar célula markdown após o gráfico com interpretação dos resultados."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 26, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 26, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 28, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 28, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 28, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 28, "type": "missing-legend", "suggestion": "Incluir legenda clara quando houver múltiplas séries (plt.legend ou ax.legend)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 32, "type": "needs-interpretation", "suggestion": "Adicionar célula markdown após o gráfico com interpretação dos resultados."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 33, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 33, "type": "missing-title", "suggestion": "Ad<PERSON><PERSON><PERSON> tí<PERSON>lo descritivo ao gráfico (plt.title ou ax.set_title)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 33, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 33, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 33, "type": "missing-legend", "suggestion": "Incluir legenda clara quando houver múltiplas séries (plt.legend ou ax.legend)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 37, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 37, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 40, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 40, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 42, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 42, "type": "missing-title", "suggestion": "Ad<PERSON><PERSON><PERSON> tí<PERSON>lo descritivo ao gráfico (plt.title ou ax.set_title)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 42, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 42, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 44, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 44, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 44, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 47, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 52, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 56, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 60, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 60, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 0, "type": "missing-context-before-code", "suggestion": "Inserir célula markdown antes desta célula explicando objetivo e o que será executado."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 11, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 11, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 11, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 11, "type": "needs-interpretation", "suggestion": "Adicionar célula markdown após o gráfico com interpretação dos resultados."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 12, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 12, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 12, "type": "missing-legend", "suggestion": "Incluir legenda clara quando houver múltiplas séries (plt.legend ou ax.legend)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 14, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 14, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 14, "type": "needs-interpretation", "suggestion": "Adicionar célula markdown após o gráfico com interpretação dos resultados."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 15, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 15, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 15, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 17, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 17, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 17, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 17, "type": "needs-interpretation", "suggestion": "Adicionar célula markdown após o gráfico com interpretação dos resultados."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 18, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 23, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 23, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 23, "type": "needs-interpretation", "suggestion": "Adicionar célula markdown após o gráfico com interpretação dos resultados."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 24, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 24, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 26, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 26, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 26, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 26, "type": "missing-legend", "suggestion": "Incluir legenda clara quando houver múltiplas séries (plt.legend ou ax.legend)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 26, "type": "needs-interpretation", "suggestion": "Adicionar célula markdown após o gráfico com interpretação dos resultados."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 27, "type": "needs-interpretation", "suggestion": "Adicionar célula markdown após o gráfico com interpretação dos resultados."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 28, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 28, "type": "missing-title", "suggestion": "Ad<PERSON><PERSON><PERSON> tí<PERSON>lo descritivo ao gráfico (plt.title ou ax.set_title)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 28, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 28, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 28, "type": "needs-interpretation", "suggestion": "Adicionar célula markdown após o gráfico com interpretação dos resultados."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 29, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 29, "type": "missing-title", "suggestion": "Ad<PERSON><PERSON><PERSON> tí<PERSON>lo descritivo ao gráfico (plt.title ou ax.set_title)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 29, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 29, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 29, "type": "missing-legend", "suggestion": "Incluir legenda clara quando houver múltiplas séries (plt.legend ou ax.legend)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 33, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 33, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 36, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 36, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 38, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 38, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 38, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 40, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 40, "type": "missing-xlabel", "suggestion": "Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 40, "type": "missing-y<PERSON>bel", "suggestion": "Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel)."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 43, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 48, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 52, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\preprocessamento\\eda_geografico.ipynb", "cell": 0, "type": "missing-context-before-code", "suggestion": "Inserir célula markdown antes desta célula explicando objetivo e o que será executado."}, {"notebook": "notebooks\\preprocessamento\\eda_geografico.ipynb", "cell": 3, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\preprocessamento\\eda_geografico.ipynb", "cell": 7, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\preprocessamento\\eda_geografico.ipynb", "cell": 18, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\preprocessamento\\eda_geografico.ipynb", "cell": 25, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\preprocessamento\\eda_geografico.ipynb", "cell": 27, "type": "low-comment-density", "suggestion": "Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas."}, {"notebook": "notebooks\\preprocessamento\\eda_geografico.ipynb", "cell": 30, "type": "needs-interpretation", "suggestion": "Adicionar célula markdown após o gráfico com interpretação dos resultados."}, {"notebook": "notebooks\\preprocessamento\\eda_geografico.ipynb", "cell": 31, "type": "needs-interpretation", "suggestion": "Adicionar célula markdown após o gráfico com interpretação dos resultados."}, {"notebook": "notebooks\\preprocessamento\\eda_geografico.ipynb", "cell": 32, "type": "missing-legend", "suggestion": "Incluir legenda clara quando houver múltiplas séries (plt.legend ou ax.legend)."}]}