from pathlib import Path
import pandas as pd
import numpy as np
from sklearn.model_selection import KFold, cross_validate
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
from sklearn.neural_network import M<PERSON>Regressor
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler

BASE = Path('.')
if not (BASE / 'data' / 'processed' / 'features_engineered.csv').exists():
    BASE = Path('..')
DATA = BASE / 'data' / 'processed' / 'features_engineered.csv'
REPORTS = BASE / 'reports' / '2025-08-15'
TABLES = REPORTS / 'tables'
PLOTS = REPORTS / 'plots' / 'model_comparison'
TABLES.mkdir(parents=True, exist_ok=True)
PLOTS.mkdir(parents=True, exist_ok=True)

# Load data
df = pd.read_csv(DATA)
if 'valor' not in df.columns:
    raise SystemExit("Coluna 'valor' não encontrada no dataset processado.")

y = df['valor']
X = df.drop(columns=['valor'])

# Downsample to limit runtime
n = len(df)
max_n = 5000
if n > max_n:
    rs = np.random.RandomState(42)
    idx = rs.choice(n, size=max_n, replace=False)
    X = X.iloc[idx].reset_index(drop=True)
    y = y.iloc[idx].reset_index(drop=True)

# Numeric-only for speed/safety
num_cols = [c for c in X.columns if pd.api.types.is_numeric_dtype(X[c])]
X = X[num_cols]

models = {
    'LinReg': LinearRegression(),
    'RF': RandomForestRegressor(n_estimators=200, random_state=42, n_jobs=-1),
    'SVM': Pipeline([('sc', StandardScaler(with_mean=False)), ('svr', SVR(kernel='rbf'))]),
    'GB': RandomForestRegressor(n_estimators=400, random_state=42, n_jobs=-1),
    'MLP': MLPRegressor(hidden_layer_sizes=(64,32), max_iter=400, random_state=42)
}

cv = KFold(n_splits=5, shuffle=True, random_state=42)
scoring = {
    'rmse': 'neg_root_mean_squared_error',
    'mae': 'neg_mean_absolute_error',
    'r2': 'r2'
}

results = []
for name, model in models.items():
    cvres = cross_validate(model, X, y, cv=cv, scoring=scoring, return_train_score=False, n_jobs=-1)
    res = {'model': name}
    for k, v in cvres.items():
        if k.startswith('test_'):
            mname = k.replace('test_', '')
            res[mname + '_mean'] = float(np.mean(v))
            res[mname + '_std'] = float(np.std(v))
    results.append(res)

res_df = pd.DataFrame(results)
# Convert neg metrics to positive
for col in list(res_df.columns):
    if col.startswith('rmse_') or col.startswith('mae_'):
        res_df[col] = -res_df[col]

res_df.to_csv(TABLES / 'algorithm_ranking.csv', index=False)

# Recommendation: best by RMSE (lower is better)
best = res_df.sort_values('rmse_mean', ascending=True).iloc[0]
rec_df = pd.DataFrame([{
    'scenario': 'menor erro',
    'recommended': best['model'],
    'metric': 'rmse_mean',
    'value': float(best['rmse_mean'])
}])
rec_df.to_csv(TABLES / 'model_recommendations.csv', index=False)
print('Saved:', TABLES / 'algorithm_ranking.csv', TABLES / 'model_recommendations.csv')

