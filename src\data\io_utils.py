"""Funções utilitárias de entrada e saída de dados.

Este módulo centraliza operações de:
- Carregamento de dados de vendas
- Validação de estrutura e conteúdo
- Limpeza e transformação de dados
- Salvamento em múltiplos formatos
"""
from __future__ import annotations
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple

# Colunas que devem estar presentes no DataFrame para análise
REQUIRED_COLS = ["data", "id_loja", "valor"]

# Conjunto com todas as UFs válidas do Brasil para validação
UF_VALIDAS = {"AC","AL","AM","AP","BA","CE","DF","ES","GO","MA","MG","MS","MT",
              "PA","PB","PE","PI","PR","RJ","RN","RO","RR","RS","SC","SE","SP","TO"}



#Renomeia colunas conforme mapeamento e verifica presença de colunas obrigatórias.
def map_and_validate(df: pd.DataFrame, mapping: Dict[str, str]) -> pd.DataFrame:

    # Aplica o mapeamento de nomes de colunas
    df = df.rename(columns=mapping)
    
    # Verifica se todas as colunas obrigatórias estão presentes
    faltantes = [c for c in REQUIRED_COLS if c not in df.columns]
    if faltantes:
        raise ValueError(f"Colunas obrigatórias ausentes após mapeamento: {faltantes}")
    return df

#Força tipos de dados apropriados para cada coluna
def coerce_types(df: pd.DataFrame) -> pd.DataFrame:
   
    if 'data' in df.columns:
        df['data'] = pd.to_datetime(df['data'], errors='coerce')
    if 'valor' in df.columns:
        # Converte para centavos e arredonda para inteiro
        df['valor'] = (pd.to_numeric(df['valor'], errors='coerce').fillna(0) * 100).round().astype('Int64')
    if 'qtd' in df.columns:
        df['qtd'] = pd.to_numeric(df['qtd'], errors='coerce')
    if 'uf' in df.columns:
        # Padroniza UFs e invalida valores incorretos
        df['uf'] = df['uf'].str.upper().str.strip()
        df.loc[~df['uf'].isin(UF_VALIDAS), 'uf'] = pd.NA
    return df

    # Realiza limpeza básica dos dados
def basic_clean(df: pd.DataFrame) -> pd.DataFrame:

    
    df = df.dropna(subset=['data', 'id_loja'])  # Remove linhas sem identificadores
    df = df.sort_values('data')                 # Ordena cronologicamente
    df = df.drop_duplicates()                   # Remove duplicatas
    return df

#Gera relatório de qualidade dos dados com métricas básicas
def qa_report(df: pd.DataFrame) -> pd.DataFrame:
 
    info = {
        'n_rows': len(df),                          # Total de linhas
        'n_cols': len(df.columns),                  # Total de colunas
        'n_nulls': int(df.isna().sum().sum()),      # Total de valores nulos
        'n_dups': int(df.duplicated().sum()),       # Total de duplicatas
    }
    # Adiciona métricas específicas se as colunas existirem
    if 'data' in df.columns:
        info['min_date'] = df['data'].min()         # Data mais antiga
        info['max_date'] = df['data'].max()         # Data mais recente
    if 'id_loja' in df.columns:
        info['lojas_unicas'] = df['id_loja'].nunique()  # Número de lojas
    if 'valor' in df.columns:
        info['valor_total'] = int(df['valor'].sum())    # Valor total
    return pd.DataFrame([info])

#Salva dados limpos e relatório QA em múltiplos formatos
def save_outputs(df: pd.DataFrame, clean_dir: Path, reports_tables: Path):
   
    # Cria diretórios se não existirem
    clean_dir.mkdir(parents=True, exist_ok=True)
    reports_tables.mkdir(parents=True, exist_ok=True)
    
    # Define caminhos dos arquivos
    csv_path = clean_dir / 'cleaned.csv'
    pq_path = clean_dir / 'cleaned.parquet'
    
    # Salva em CSV (sempre) e tenta salvar em Parquet (opcional)
    df.to_csv(csv_path, index=False)
    try:
        df.to_parquet(pq_path, index=False)
    except Exception:
        pass
    
    # Salva relatório QA
    qa = qa_report(df)
    qa.to_csv(reports_tables / 'qa_overview.csv', index=False)
    
    return csv_path, pq_path

# Define quais funções e constantes podem ser importadas
__all__ = [
    'map_and_validate',    # Mapeia e valida colunas
    'coerce_types',        # Força tipos de dados
    'basic_clean',         # Limpeza básica
    'qa_report',          # Relatório de qualidade
    'save_outputs',       # Salva resultados
    'REQUIRED_COLS'       # Colunas obrigatórias
]
