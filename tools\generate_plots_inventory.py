from pathlib import Path
import csv

BASE = Path('.')
PRESENT = BASE/'reports'/'2025-08-15'/'plots'/'presentation'
OUT = BASE/'reports'/'2025-08-15'/'plots'/'plots_inventory.csv'

rows = []
for p in sorted(PRESENT.glob('*.png')):
    rows.append({
        'file_name': p.name,
        'path': str(p),
        'size_bytes': p.stat().st_size,
        'modified': p.stat().st_mtime
    })

OUT.parent.mkdir(parents=True, exist_ok=True)
with open(OUT, 'w', newline='', encoding='utf-8') as f:
    w = csv.DictWriter(f, fieldnames=['file_name','path','size_bytes','modified'])
    w.writeheader()
    w.writerows(rows)

print('Inventory saved to', OUT)

