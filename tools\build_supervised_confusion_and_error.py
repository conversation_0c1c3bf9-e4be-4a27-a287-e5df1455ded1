from pathlib import Path
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import KFold
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error, precision_recall_fscore_support

from feature_filter import compute_clean_features

BASE = Path('.')
REPORTS = BASE/'reports'/'2025-08-15'
PLOTS = REPORTS/'plots'/'presentation'/'slides'
TABLES = REPORTS/'tables'
PLOTS.mkdir(parents=True, exist_ok=True)
TABLES.mkdir(parents=True, exist_ok=True)
DATA = BASE/'data'/'processed'/'features_engineered_regional.csv'

# Load data
df = pd.read_csv(DATA, low_memory=False)
TARGET = 'valor' if 'valor' in df.columns else df.select_dtypes('number').columns[-1]
clean_cols, _ = compute_clean_features(df, TARGET, REPORTS)
X = df[clean_cols].select_dtypes('number').fillna(0.0)
y = pd.to_numeric(df[TARGET], errors='coerce').fillna(0.0).values

# Territorial clusters (K=7)
clus_path = TABLES/'cluster_assignments_territorial.csv'
if clus_path.exists():
    clus = pd.read_csv(clus_path)['cluster'].values
else:
    # fallback para clusters originais
    clus = pd.read_csv(TABLES/'cluster_assignments.csv')['cluster'].values

# Out-of-fold predictions para avaliação honesta
kf = KFold(n_splits=5, shuffle=True, random_state=42)
y_pred = np.zeros_like(y, dtype=float)
for train_idx, test_idx in kf.split(X):
    sc = StandardScaler(with_mean=False)
    Xtr = sc.fit_transform(X.iloc[train_idx])
    Xte = sc.transform(X.iloc[test_idx])
    rf = RandomForestRegressor(n_estimators=200, random_state=42, n_jobs=-1)
    rf.fit(Xtr, y[train_idx])
    y_pred[test_idx] = rf.predict(Xte)

# Erros
eps = 1e-6
residual = y - y_pred
ape = np.abs(residual) / (np.abs(y) + eps)  # Absolute Percentage Error

# Categorias de erro (regressão)
# Baixo <=10%, Médio 10-25%, Alto >25%
err_cat = pd.cut(ape, bins=[-np.inf, 0.10, 0.25, np.inf], labels=['Baixo (<=10%)','Médio (10-25%)','Alto (>25%)'])

# Discretização do alvo para matriz de confusão de classificação
q = np.quantile(y, [0.0, 1/3, 2/3, 1.0])
# garantir limites estritamente crescentes
for i in range(1, len(q)):
    if q[i] <= q[i-1]:
        q[i] = q[i-1] + 1e-6

true_bins = pd.Series(pd.cut(y, bins=q, labels=['Baixo','Médio','Alto'], include_lowest=True), name='real')
pred_bins = pd.Series(pd.cut(y_pred, bins=q, labels=['Baixo','Médio','Alto'], include_lowest=True), name='previsto')

# Confusion matrix (classificação por faixas)
cm = pd.crosstab(true_bins, pred_bins).reindex(index=['Baixo','Médio','Alto'], columns=['Baixo','Médio','Alto'])
cm_path = TABLES/'supervised_confusion_bins.csv'
cm.to_csv(cm_path)

# Métricas por classe
# Converter para inteiros 0,1,2
true_idx = true_bins.astype('category').cat.codes.values
pred_idx = pred_bins.astype('category').cat.codes.values
mask = (true_idx != -1) & (pred_idx != -1)
prec, rec, f1, support = precision_recall_fscore_support(true_idx[mask], pred_idx[mask], labels=[0,1,2], zero_division=0)
metrics_cls = pd.DataFrame({
    'classe': ['Baixo','Médio','Alto'],
    'precision': prec,
    'recall': rec,
    'f1': f1,
    'support': support
})
metrics_cls_path = TABLES/'supervised_classification_metrics_by_class.csv'
metrics_cls.to_csv(metrics_cls_path, index=False)

# Métricas por cluster (regressão)
rows = []
for c in sorted(np.unique(clus)):
    idx = (clus == c)
    if idx.sum() == 0:
        continue
    r2 = r2_score(y[idx], y_pred[idx]) if idx.sum()>1 else np.nan
    mae = mean_absolute_error(y[idx], y_pred[idx])
    rmse = mean_squared_error(y[idx], y_pred[idx], squared=False)
    mape = float(np.nanmean(ape[idx]))
    rows.append({'cluster': int(c), 'n': int(idx.sum()), 'r2': r2, 'rmse': rmse, 'mae': mae, 'mape': mape})
by_cluster = pd.DataFrame(rows).sort_values('cluster')
by_cluster_path = TABLES/'supervised_performance_by_territorial_cluster.csv'
by_cluster.to_csv(by_cluster_path, index=False)

# ---------- Plots ----------
sns.set_style('whitegrid')

# 1) Matriz de confusão (classificação por faixas)
plt.figure(figsize=(8,6))
sns.heatmap(cm, annot=True, fmt='d', cmap='YlGnBu')
plt.title('Matriz de Confusão — Faixas de Valor (Baixo/Médio/Alto)')
plt.xlabel('Previsto'); plt.ylabel('Real')
plt.tight_layout(); plt.savefig(PLOTS/'confusion_matrix_value_bins_v1.png', dpi=320); plt.close()

# 2) Métricas por classe (barras)
plt.figure(figsize=(10,6))
metrics_plot = metrics_cls.melt(id_vars='classe', value_vars=['precision','recall','f1'], var_name='métrica', value_name='valor')
sns.barplot(data=metrics_plot, x='classe', y='valor', hue='métrica', palette='Blues')
plt.ylim(0,1.0)
plt.title('Precisão, Recall e F1 por Classe (Baixo/Médio/Alto)')
plt.tight_layout(); plt.savefig(PLOTS/'classification_metrics_by_class_v1.png', dpi=320); plt.close()

# 3) Distribuição de erro percentual por cluster (boxplot)
plt.figure(figsize=(12,7))
sns.boxplot(x=clus, y=ape, palette='tab20')
plt.xlabel('Cluster territorial (0–6)'); plt.ylabel('Erro percentual absoluto (APE)')
plt.title('Distribuição de erro por cluster territorial')
plt.tight_layout(); plt.savefig(PLOTS/'error_mape_by_cluster_box_v1.png', dpi=320); plt.close()

# 4) Matriz de erros categorizados (regressão)
err_cm = pd.crosstab(true_bins, err_cat).reindex(index=['Baixo','Médio','Alto'])
plt.figure(figsize=(8,6))
sns.heatmap(err_cm, annot=True, fmt='d', cmap='OrRd')
plt.title('Matriz de Erros Categorizados por Faixa Real')
plt.xlabel('Categoria de Erro'); plt.ylabel('Faixa Real')
plt.tight_layout(); plt.savefig(PLOTS/'error_category_matrix_v1.png', dpi=320); plt.close()

# 5) Heatmap: MAPE médio por cluster x faixa prevista
pred_bins_series = pd.Series(pred_bins, name='faixa_prevista')
clus_series = pd.Series(clus, name='cluster')
heat_df = pd.DataFrame({'cluster': clus_series, 'faixa_prevista': pred_bins_series, 'ape': ape})
heat_pivot = heat_df.pivot_table(index='cluster', columns='faixa_prevista', values='ape', aggfunc='mean')
plt.figure(figsize=(9,6))
sns.heatmap(heat_pivot, annot=True, fmt='.2f', cmap='YlOrBr')
plt.title('MAPE médio por Cluster x Faixa Prevista')
plt.xlabel('Faixa Prevista'); plt.ylabel('Cluster territorial')
plt.tight_layout(); plt.savefig(PLOTS/'mape_heatmap_cluster_predtier_v1.png', dpi=320); plt.close()

print('Saved:')
print(' -', cm_path)
print(' -', metrics_cls_path)
print(' -', by_cluster_path)
print(' - plots in', PLOTS)

