{"cells": [{"cell_type": "code", "execution_count": null, "id": "83842507", "metadata": {}, "outputs": [], "source": ["# [anti-id] helpers – escolher dimensão de negócio em vez de IDs\n", "SAFE_DIM_PRIORITY = ['UF','uf','Cidade','cidade','Tipo_PDV','tipo_pdv','Estado_Emp','estado_emp']\n", "def SAFE_DIM_OF(df_like):\n", "    try:\n", "        cols = list(df_like.columns)\n", "    except Exception:\n", "        cols = []\n", "    for c in SAFE_DIM_PRIORITY:\n", "        if c in cols:\n", "            return c\n", "    return SAFE_DIM_PRIORITY[0]\n", "# Var global padrão – tenta inferir de df se existir, senão usa primeira opção\n", "try:\n", "    SAFE_DIM = SAFE_DIM_OF(df)\n", "except Exception:\n", "    SAFE_DIM = SAFE_DIM_PRIORITY[0]\n"]}, {"cell_type": "code", "execution_count": null, "id": "a61a73cb", "metadata": {}, "outputs": [], "source": ["\n", "# [anti-id] addendum: funções auxiliares adicionais\n", "BUSINESS_ENTITY_PRIORITY = ['id_loja','id_cliente','id_vendedor','id_produto']\n", "def BUSINESS_ENTITY_DIM(df_like):\n", "    try:\n", "        cols = list(df_like.columns)\n", "    except Exception:\n", "        cols = []\n", "    for c in BUSINESS_ENTITY_PRIORITY:\n", "        if c in cols:\n", "            return c\n", "    return SAFE_DIM_OF(df_like)\n", "def UNIQUE_KEYS(*keys):\n", "    out = []\n", "    seen = set()\n", "    for k in keys:\n", "        if isinstance(k, (list, tuple)):\n", "            for x in k:\n", "                if x and x not in seen:\n", "                    seen.add(x); out.append(x)\n", "        else:\n", "            if k and k not in seen:\n", "                seen.add(k); out.append(k)\n", "    return out\n"]}, {"cell_type": "code", "execution_count": null, "id": "7956ccf2", "metadata": {}, "outputs": [], "source": ["\n", "# [anti-id] addendum: funções auxiliares adicionais\n", "BUSINESS_ENTITY_PRIORITY = ['id_loja','id_cliente','id_vendedor','id_produto']\n", "def BUSINESS_ENTITY_DIM(df_like):\n", "    try:\n", "        cols = list(df_like.columns)\n", "    except Exception:\n", "        cols = []\n", "    for c in BUSINESS_ENTITY_PRIORITY:\n", "        if c in cols:\n", "            return c\n", "    return SAFE_DIM_OF(df_like)\n", "def UNIQUE_KEYS(*keys):\n", "    out = []\n", "    seen = set()\n", "    for k in keys:\n", "        if isinstance(k, (list, tuple)):\n", "            for x in k:\n", "                if x and x not in seen:\n", "                    seen.add(x); out.append(x)\n", "        else:\n", "            if k and k not in seen:\n", "                seen.add(k); out.append(k)\n", "    return out\n"]}, {"cell_type": "markdown", "id": "5b2824c3", "metadata": {}, "source": ["### [auto-doc] Etapa 1\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "35631ae0", "metadata": {}, "outputs": [], "source": ["# [auto-doc] estilo global\n", "import matplotlib as mpl, seaborn as sns\n", "import matplotlib.pyplot as plt\n", "sns.set_theme(style='whitegrid', context='notebook', palette='deep')\n", "mpl.rcParams.update({'figure.dpi': 150, 'axes.titlesize': 12, 'axes.labelsize': 11, 'legend.fontsize': 10})\n", "\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.title('<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> tí<PERSON>lo descritivo (ajustar)')\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "e1021c60", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "4e9e454e", "metadata": {}, "source": ["# Comparação de Modelos (Colab) — Chilli Beans\n", "\n", "Notebook de comparação de algoritmos com validação cruzada, tuning de hiperparâmetros e explicabilidade.\n", "\n", "- 3+ modelos candidatos\n", "- Grid/Random Search para tuning\n", "- Artefatos exportados para reports/2025-08-15/\n"]}, {"cell_type": "markdown", "id": "b12bdedd", "metadata": {}, "source": ["## 1. <PERSON><PERSON> <PERSON> (Colab)"]}, {"cell_type": "markdown", "id": "88033673", "metadata": {}, "source": ["### [auto-doc] Etapa 2\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": 1, "id": "f3adbfaa", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:14:04.240508Z", "iopub.status.busy": "2025-09-15T15:14:04.240508Z", "iopub.status.idle": "2025-09-15T15:14:04.250107Z", "shell.execute_reply": "2025-09-15T15:14:04.249571Z"}}, "outputs": [], "source": ["#@title Instalação de dependências (apenas Colab)\n", "import sys, subprocess\n", "IN_COLAB = 'google.colab' in sys.modules\n", "if IN_COLAB:\n", "    pkgs = ['pandas>=1.5.0','numpy>=1.21.0','scikit-learn>=1.1.0',\n", "            'seaborn>=0.12.0','matplotlib>=3.5.0','shap>=0.45.0']\n", "    subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-q'] + pkgs)\n"]}, {"cell_type": "markdown", "id": "81cc790a", "metadata": {}, "source": ["## 2. Carregamento de Dados e Detecção de Tarefa"]}, {"cell_type": "markdown", "id": "468f2b1d", "metadata": {}, "source": ["### [auto-doc] Etapa 3\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "2ed08c5d", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:14:04.253596Z", "iopub.status.busy": "2025-09-15T15:14:04.252624Z", "iopub.status.idle": "2025-09-15T15:14:07.528916Z", "shell.execute_reply": "2025-09-15T15:14:07.527880Z"}}, "outputs": [], "source": ["from pathlib import Path\n", "import pandas as pd, numpy as np\n", "import matplotlib.pyplot as plt, seaborn as sns\n", "from sklearn.model_selection import StratifiedKFold, KFold, cross_validate, train_test_split, RandomizedSearchCV, GridSearchCV\n", "from sklearn.metrics import (accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, average_precision_score,\n", "    mean_squared_error, r2_score, mean_absolute_error)\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.linear_model import LogisticRegression, LinearRegression\n", "from sklearn.svm import SVC, SVR\n", "from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor\n", "from sklearn.neural_network import MLPClassifier, MLPRegressor\n", "import warnings; warnings.filterwarnings('ignore')\n", "BASE = Path('.')\n", "if not (BASE/'data'/'processed').exists(): BASE = Path('..')\n", "REPORTS = BASE/'reports'/'2025-08-15'\n", "PLOTS = REPORTS/'plots'/'model_comparison'\n", "TABLES = REPORTS/'tables'\n", "for d in [PLOTS, TABLES]: d.mkdir(parents=True, exist_ok=True)\n", "# Escolha robusta do dataset\n", "DATA = None\n", "cand = [BASE/'data'/'processed'/'features_engineered_regional.csv', BASE/'data'/'processed'/'features_engineered.csv']\n", "for c in cand:\n", "    if c.exists(): DATA = c; break\n", "assert DATA is not None, 'Dataset de features não encontrado em data/processed'\n", "df = pd.read_csv(DATA)\n", "# Downsample para estabilidade\n", "MAX_N = 5000 if IN_COLAB else 4000\n", "if len(df) > MAX_N: df = df.sample(MAX_N, random_state=42).reset_index(drop=True)\n", "# Heurística do TARGET\n", "if 'valor' in df.columns:\n", "    TARGET = 'valor'\n", "else:\n", "    cand_t = [c for c in df.columns if c.lower() in ('target','label','y','classe','class')]\n", "    TARGET = cand_t[0] if cand_t else df.columns[-1]\n", "y = df[TARGET]\n", "X = df.drop(columns=[TARGET])\n", "X = X.select_dtypes(include=['number'])\n", "is_classification = (pd.api.types.is_integer_dtype(y) and y.nunique()<=10) or (y.dtype=='object')\n", "print('Dataset:', DATA.name, '| TARGET:', TARGET, '| Tarefa:', 'classificação' if is_classification else 'regressão', '| X shape:', X.shape, '| Modo:', 'Colab' if IN_COLAB else 'Local rápido')\n"]}, {"cell_type": "markdown", "id": "7dbc1068", "metadata": {}, "source": ["## 3. Modelos Candidatos e Espaços de Hiperparâmetros"]}, {"cell_type": "markdown", "id": "71083d28", "metadata": {}, "source": ["### [auto-doc] Etapa 4\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "cd69a52d", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:14:07.532487Z", "iopub.status.busy": "2025-09-15T15:14:07.531510Z", "iopub.status.idle": "2025-09-15T15:14:07.543081Z", "shell.execute_reply": "2025-09-15T15:14:07.543081Z"}}, "outputs": [], "source": ["# Nota: cé<PERSON>la extensa – inserir comentários objetivos sobre o que está sendo feito,\n", "# eventuais parâmetros relevantes e como interpretar o resultado.\n", "np.random.seed(42)\n", "if is_classification:\n", "    models = {\n", "        'LogReg': Pipeline([('sc', StandardScaler(with_mean=False)), ('clf', LogisticRegression(max_iter=500))]),\n", "        'RF': RandomForestClassifier(n_estimators=200, random_state=42),\n", "        'SVM': Pipeline([('sc', StandardScaler(with_mean=False)), ('clf', SVC(kernel='rbf', probability=True, random_state=42))])\n", "    }\n", "    param_spaces = {\n", "        'LogReg': {'clf__C': [0.1, 1, 3, 10], 'clf__penalty': ['l2'], 'clf__solver': ['lbfgs']},\n", "        'RF': {'n_estimators': [200, 400, 800], 'max_depth': [None, 10, 20, 40], 'min_samples_leaf': [1, 2, 5]},\n", "        'SVM': {'clf__C': [0.5, 1, 2, 4], 'clf__gamma': ['scale', 0.1, 0.01, 0.001]}\n", "    }\n", "    cv = StratifiedKFold(n_splits=(5 if IN_COLAB else 3), shuffle=True, random_state=42)\n", "    primary_metric = 'f1_weighted'\n", "    scoring = {\n", "        'accuracy': 'accuracy',\n", "        'precision': 'precision_weighted',\n", "        'recall': 'recall_weighted',\n", "        'f1_weighted': 'f1_weighted',\n", "        'roc_auc_ovr': 'roc_auc_ovr'\n", "    }\n", "else:\n", "    models = {\n", "        'LinReg': LinearRegression(),\n", "        'RF': RandomForestRegressor(n_estimators=300, random_state=42),\n", "        'SVR': Pipeline([('sc', StandardScaler(with_mean=False)), ('svr', SVR(kernel='rbf'))])\n", "    }\n", "    param_spaces = {\n", "        'LinReg': {},\n", "        'RF': {'n_estimators': [300, 600, 900], 'max_depth': [None, 10, 20, 40], 'min_samples_leaf': [1, 2, 4]},\n", "        'SVR': {'svr__C': [1, 5, 10, 20], 'svr__gamma': ['scale', 0.1, 0.01], 'svr__epsilon': [0.1, 0.2, 0.5]}\n", "    }\n", "    cv = KFold(n_splits=(5 if IN_COLAB else 3), shuffle=True, random_state=42)\n", "    primary_metric = 'neg_root_mean_squared_error'\n", "    scoring = {'rmse': 'neg_root_mean_squared_error', 'mae': 'neg_mean_absolute_error', 'r2': 'r2'}\n", "print('Modelos definidos:', list(models.keys()))"]}, {"cell_type": "markdown", "id": "393bc365", "metadata": {}, "source": ["## 4. <PERSON><PERSON><PERSON> (baseline, sem tuning)"]}, {"cell_type": "markdown", "id": "0ac65842", "metadata": {}, "source": ["### [auto-doc] Etapa 5\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "d98d53a9", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:14:07.546999Z", "iopub.status.busy": "2025-09-15T15:14:07.546999Z", "iopub.status.idle": "2025-09-15T15:14:39.424652Z", "shell.execute_reply": "2025-09-15T15:14:39.424652Z"}}, "outputs": [], "source": ["# Nota: cé<PERSON>la extensa – inserir comentários objetivos sobre o que está sendo feito,\n", "# eventuais parâmetros relevantes e como interpretar o resultado.\n", "results = []\n", "for name, model in models.items():\n", "    cvres = cross_validate(model, X, y, cv=cv, scoring=scoring, return_train_score=False, n_jobs=1)\n", "    row = {'model': name}\n", "    for k,v in cvres.items():\n", "        if k.startswith('test_'):\n", "            mname = k.replace('test_','')\n", "            row[mname+'_mean'] = float(np.mean(v))\n", "            row[mname+'_std'] = float(np.std(v))\n", "    results.append(row)\n", "res_df = pd.DataFrame(results)\n", "res_df.to_csv(TABLES / 'algorithm_ranking_baseline.csv', index=False)\n", "res_df"]}, {"cell_type": "markdown", "id": "3243b20a", "metadata": {}, "source": ["## 5. <PERSON><PERSON> Hiperparâmetros (Random/Grid Search)"]}, {"cell_type": "markdown", "id": "7794d583", "metadata": {}, "source": ["### [auto-doc] Etapa 6\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "078617b1", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:14:39.428564Z", "iopub.status.busy": "2025-09-15T15:14:39.427588Z", "iopub.status.idle": "2025-09-15T15:21:32.121386Z", "shell.execute_reply": "2025-09-15T15:21:32.120638Z"}}, "outputs": [], "source": ["best_rows = []\n", "best_estimators = {}\n", "for name, model in models.items():\n", "    params = param_spaces.get(name, {})\n", "    if not params:\n", "        # sem hiperparâmetros — usa o próprio\n", "        estimator = model\n", "    else:\n", "        # estratégia mista: RandomizedSearch para grades maiores, <PERSON>rid pequeno para demais\n", "        n_combinations = 1\n", "        for v in params.values(): n_combinations *= len(v)\n", "        max_iter = (20 if IN_COLAB else 8)\n", "        if n_combinations > max_iter:\n", "            search = RandomizedSearchCV(model, params, n_iter=min(max_iter, n_combinations), scoring=primary_metric, cv=cv, random_state=42, n_jobs=1)\n", "        else:\n", "            search = GridSearchCV(model, params, scoring=primary_metric, cv=cv, n_jobs=1)\n", "        search.fit(X, y)\n", "        estimator = search.best_estimator_\n", "    # avaliar com cross_validate para métricas completas\n", "    cvres = cross_validate(estimator, X, y, cv=cv, scoring=scoring, return_train_score=False, n_jobs=1)\n", "    row = {'model': name, 'tuned': True}\n", "    try:\n", "        row['best_params'] = getattr(search, 'best_params_', {}) if params else {}\n", "    except Exception:\n", "        row['best_params'] = {}\n", "    for k,v in cvres.items():\n", "        if k.startswith('test_'):\n", "            mname = k.replace('test_','')\n", "            row[mname+'_mean'] = float(np.mean(v))\n", "            row[mname+'_std'] = float(np.std(v))\n", "    best_rows.append(row)\n", "    best_estimators[name] = estimator\n", "best_df = pd.DataFrame(best_rows)\n", "# expandir best_params em colunas texto\n", "best_df['best_params'] = best_df['best_params'].apply(lambda d: str(d))\n", "best_df.to_csv(TABLES / 'algorithm_ranking_tuned.csv', index=False)\n", "best_df\n"]}, {"cell_type": "markdown", "id": "30872536", "metadata": {}, "source": ["## 6. Visualizações de Performance"]}, {"cell_type": "markdown", "id": "a1ecc160", "metadata": {}, "source": ["### [auto-doc] Etapa 7\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "160a3daa", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:21:32.123501Z", "iopub.status.busy": "2025-09-15T15:21:32.123501Z", "iopub.status.idle": "2025-09-15T15:21:32.393042Z", "shell.execute_reply": "2025-09-15T15:21:32.392059Z"}}, "outputs": [], "source": ["# Nota: cé<PERSON>la extensa – inserir comentários objetivos sobre o que está sendo feito,\n", "# eventuais parâmetros relevantes e como interpretar o resultado.\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "PLOTS.mkdir(parents=True, exist_ok=True)\n", "if is_classification:\n", "    plt.figure(figsize=(7,4));\n", "    sns.barplot(data=best_df, x='model', y='f1_weighted_mean');\n", "    plt.title('F1 (weighted) — Tuned'); plt.tight_layout(); plt.savefig(PLOTS/'bar_f1_tuned.png'); plt.show()\n", "else:\n", "    plt.figure(figsize=(7,4));\n", "    # lembrar: valores são negativos em 'neg_root_mean_squared_error' — converter\n", "    tmp = best_df.copy()\n", "    if 'rmse_mean' in tmp.columns: tmp['rmse_pos'] = tmp['rmse_mean'].abs()\n", "    sns.barplot(data=tmp, x='model', y='rmse_pos');\n", "    plt.ylabel('RMSE (médio)'); plt.title('RMSE — Tuned'); plt.tight_layout(); plt.savefig(PLOTS/'bar_rmse_tuned.png'); plt.show()\n", "\n", "# Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.xlabel('Eixo X (ajustar)')"]}, {"cell_type": "markdown", "id": "0c727779", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "e0b0cef6", "metadata": {}, "source": ["## 7. Explicabilidade (SHAP ou Importâncias)"]}, {"cell_type": "markdown", "id": "c173c826", "metadata": {}, "source": ["### [auto-doc] Etapa 8\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "b32436ba", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:21:32.396951Z", "iopub.status.busy": "2025-09-15T15:21:32.395972Z", "iopub.status.idle": "2025-09-15T15:21:43.250991Z", "shell.execute_reply": "2025-09-15T15:21:43.250991Z"}}, "outputs": [], "source": ["expl_model_name = 'RF' if 'RF' in best_estimators else list(best_estimators.keys())[0]\n", "expl = best_estimators[expl_model_name]\n", "print('Modelo para explicabilidade:', expl_model_name)\n", "# Ajuste final em amostra para SHAP (evitar custo alto)\n", "X_small = X.sample(min((2000 if IN_COLAB else 800), len(X)), random_state=42)\n", "y_small = y.loc[X_small.index]\n", "expl.fit(X_small, y_small)\n", "# Tentar SHAP; se falhar, usar feature_importances_\n", "try:\n", "    import shap\n", "    shap_explainer = None\n", "    if hasattr(expl, 'predict_proba') or isinstance(expl, RandomForestClassifier) or isinstance(expl, RandomForestRegressor):\n", "        shap_explainer = shap.TreeExplainer(expl)\n", "    if shap_explainer is not None:\n", "        shap_values = shap_explainer.shap_values(X_small)\n", "        plt.figure(figsize=(10,6))\n", "        shap.summary_plot(shap_values if isinstance(shap_values, list) else shap_values, X_small, show=False)\n", "        plt.tight_layout(); plt.savefig(PLOTS/'shap_summary.png', dpi=200); plt.close()\n", "    else:\n", "        raise RuntimeError('SHAP não suportado para este estimador; usando importâncias')\n", "except Exception as e:\n", "    print('Falha/indisponibilidade SHAP, usando importâncias:', e)\n", "    try:\n", "        importances = getattr(expl, 'feature_importances_', None)\n", "        if importances is not None:\n", "            s = pd.Series(importances, index=X_small.columns).sort_values(ascending=False).head(20)\n", "            plt.figure(figsize=(10,6)); s[::-1].plot(kind='barh'); plt.title('Importância de Features (Top 20)'); plt.tight_layout(); plt.savefig(PLOTS/'feature_importance.png', dpi=200); plt.close()\n", "    except Exception as e2:\n", "        print('Sem importâncias disponíveis:', e2)\n", "\n", "# Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "933dcf8a", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "7d07ba51", "metadata": {}, "source": ["## 8. Recomendaç<PERSON>es de Modelo"]}, {"cell_type": "markdown", "id": "c788c6fc", "metadata": {}, "source": ["### [auto-doc] Etapa 9\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "1b148e27", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:21:43.253926Z", "iopub.status.busy": "2025-09-15T15:21:43.253926Z", "iopub.status.idle": "2025-09-15T15:21:43.267814Z", "shell.execute_reply": "2025-09-15T15:21:43.267275Z"}}, "outputs": [], "source": ["# Nota: cé<PERSON>la extensa – inserir comentários objetivos sobre o que está sendo feito,\n", "# eventuais parâmetros relevantes e como interpretar o resultado.\n", "recs = []\n", "if is_classification:\n", "    best = best_df.sort_values('f1_weighted_mean', ascending=False).iloc[0]\n", "    recs.append({'scenario':'equilíbrio precisão/recall','recommended': best['model'],'metric': 'f1_weighted_mean', 'value': float(best['f1_weighted_mean'])})\n", "else:\n", "    # menor RMSE\n", "    tmp = best_df.copy(); tmp['rmse_pos'] = tmp['rmse_mean'].abs() if 'rmse_mean' in tmp.columns else np.nan\n", "    best = tmp.sort_values('rmse_pos', ascending=True).iloc[0]\n", "    recs.append({'scenario':'menor erro (RMSE)','recommended': best['model'], 'metric': 'rmse', 'value': float(best['rmse_pos'])})\n", "rec_df = pd.DataFrame(recs)\n", "rec_df.to_csv(TABLES/'model_recommendations_tuned.csv', index=False)\n", "rec_df"]}, {"cell_type": "markdown", "id": "5e8a195f", "metadata": {}, "source": ["## 9. <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "\n", "- Este notebook implementa comparação, tuning e explicabilidade com artefatos exportados para `reports/2025-08-15/`.\n", "- Para detalhes de métricas agregadas do pipeline atual, consulte também `reports/2025-08-15/tables/algorithm_ranking.csv`."]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}