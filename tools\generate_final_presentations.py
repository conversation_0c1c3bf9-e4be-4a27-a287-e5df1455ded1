import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score

BASE = Path('.')
REPORTS = BASE/'reports'/'2025-08-15'
TABLES = REPORTS/'tables'
PLOTS = REPORTS/'plots'
PRESENT = PLOTS/'presentation'
PRESENT.mkdir(parents=True, exist_ok=True)

DATA = BASE/'data'/'processed'/'features_engineered_regional.csv'
df = pd.read_csv(DATA, low_memory=False)

# cluster labels e nomes de negócio
assign_path = TABLES/'cluster_assignments.csv'
if assign_path.exists():
    lab = pd.read_csv(assign_path)
    labels = lab['cluster'].values if 'cluster' in lab.columns else np.zeros(len(df), dtype=int)
else:
    reg = [c for c in df.columns if 'REGIAO_CHILLI' in c]
    labels = np.argmax(df[reg].values, axis=1) if reg else np.zeros(len(df), dtype=int)

names_path = TABLES/'cluster_business_names.csv'
if names_path.exists():
    names_df = pd.read_csv(names_path)
else:
    u = sorted(pd.Series(labels).unique())
    names_df = pd.DataFrame({'cluster': u, 'business_name': [f'Cluster {int(x)}' for x in u]})
name_map = {int(r.cluster): r.business_name for _, r in names_df.iterrows()}

# métricas por cluster (r2, rmse, mae)
metrics_path = TABLES/'supervised_by_cluster_metrics_ci.csv'
metrics_df = pd.read_csv(metrics_path) if metrics_path.exists() else pd.DataFrame()

# cores fixas por cluster
clusters = sorted(pd.Series(labels).unique())
palette = sns.color_palette('tab10', n_colors=max(10, len(clusters)))
color_map = {int(c): palette[i % len(palette)] for i, c in enumerate(clusters)}
name_color = {name_map[c]: color_map[c] for c in clusters}

# Visualização 2: Comparação de Receita por Cluster (overlapping)
revenue_col = 'valor'
df['_cluster'] = labels
agg = df.groupby('_cluster')[revenue_col].agg(['mean','median','count','std']).reset_index()
agg['se'] = agg['std'] / np.sqrt(agg['count'].clip(lower=1))
agg['ci_low'] = agg['mean'] - 1.96*agg['se']
agg['ci_high'] = agg['mean'] + 1.96*agg['se']
agg['name'] = agg['_cluster'].map(name_map)

plt.figure(figsize=(10,6))
order = agg.sort_values('mean', ascending=False)
colors = [name_color[n] for n in order['name']]
plt.bar(order['name'], order['mean'], color=colors, alpha=0.9, label='Média de Receita')
plt.errorbar(order['name'], order['mean'], yerr=1.96*order['se'], fmt='none', ecolor='black', elinewidth=1, capsize=3, label='IC 95%')
plt.xticks(rotation=30, ha='right')
plt.ylabel('Receita média (R$)'); plt.title('Comparação de Receita por Cluster — com IC95%')
plt.legend(loc='upper right')
# So What + metodologia
plt.gcf().text(0.01, 0.02, 'So What?: priorizar clusters com maior receita média e ICs estreitos; revisar outliers onde ICs são largos.\\nFonte: features_engineered_regional.csv • Metodologia: média ± IC95%.', ha='left', va='bottom', fontsize=9)
plt.tight_layout(rect=[0,0.04,1,1])
plt.savefig(PRESENT/'v2_revenue_comparison_clusters.png', dpi=220)
plt.close()

# Visualização 3: Precisão do Clustering (K-Means vs Atual)
# features simples para comparação (evitando alvo)
ban = {'valor','sq_valor','valor_boxcox','store_avg_valor','store_avg_valor_boxcox'}
Xcols = [c for c in df.columns if c not in ban and df[c].dtype != 'object' and not c.startswith('UF_')]
X = df[Xcols].fillna(0.0).values
k = len(clusters) if len(clusters) >= 2 else 3
km = KMeans(n_clusters=k, n_init='auto', random_state=42)
km_labels = km.fit_predict(X)

# silhouette
sil_current = silhouette_score(X, labels) if len(set(labels))>1 else np.nan
sil_km = silhouette_score(X, km_labels) if len(set(km_labels))>1 else np.nan

# inertia (apenas KMeans)
inertia_km = km.inertia_

fig, ax = plt.subplots(1,1, figsize=(10,6))
ax.bar(['Atual (Silhouette)','K-Means (Silhouette)'], [sil_current, sil_km], color=['#1f77b4','#ff7f0e'])
ax.set_ylim(0, 1)
ax2 = ax.twinx()
ax2.plot(['Atual (Silhouette)','K-Means (Silhouette)'], [np.nan, np.nan], color='gray', linestyle='--', marker='o', label='Inércia K-Means')
ax2.plot(['Atual (Silhouette)','K-Means (Silhouette)'], [inertia_km, inertia_km], color='gray', linestyle='--', marker='o')
ax.set_ylabel('Silhouette Score'); ax2.set_ylabel('Inércia (K-Means)')
ax.set_title('Precisão do Clustering — Comparação: Método Atual vs K-Means')
ax.legend(loc='upper left'); ax2.legend(loc='upper right')
plt.gcf().text(0.01, 0.02, 'So What?: se Silhouette do K-Means superar o atual de forma consistente, reavaliar método atual.\\nNota: Inércia apenas para K-Means. Mesma amostra e features (sem alvo).', ha='left', va='bottom', fontsize=9)
plt.tight_layout(rect=[0,0.04,1,1])
plt.savefig(PRESENT/'v3_clustering_precision_comparison.png', dpi=220)
plt.close()

# Visualização 4: Dashboard de KPIs e Scores
if not metrics_df.empty:
    m = metrics_df.copy()
    m['name'] = m['cluster'].map(name_map)
    fig, axes = plt.subplots(2,2, figsize=(12,8))
    # R2 por cluster
    axes[0,0].bar(m['name'], m['r2'], color=[name_color[n] for n in m['name']])
    axes[0,0].set_title('R² por Cluster'); axes[0,0].set_ylim(0,1)
    axes[0,0].tick_params(axis='x', rotation=30)
    # RMSE / MAE comparativo (overlap)
    axes[0,1].plot(m['name'], m['rmse'], marker='o', label='RMSE')
    axes[0,1].plot(m['name'], m['mae'], marker='s', label='MAE')
    axes[0,1].legend(); axes[0,1].set_title('Erros por Cluster (RMSE vs MAE)'); axes[0,1].tick_params(axis='x', rotation=30)
    # KPI geral
    axes[1,0].axis('off')
    txt = [
        'KPIs Gerais:',
        f"R² médio: {np.nanmean(m['r2']):.2f}",
        f"RMSE mediano: {np.nanmedian(m['rmse']):.2f}",
        f"MAE mediano: {np.nanmedian(m['mae']):.2f}",
        'Confiabilidade: IC95% reportado nas tabelas; ver normalidade em Q-Q plots',
    ]
    axes[1,0].text(0,1, '\n'.join(txt), va='top', fontsize=11)
    # So What
    axes[1,1].axis('off')
    so = [
        'So What?:',
        '- Clusters com R² alto e RMSE/MAE baixos: priorizar alocação e campanhas segmentadas',
        '- Clusters com erro alto: revisar features, granularidade e possíveis outliers',
        '- Se K-Means > Atual em Silhouette: reavaliar método atual',
    ]
    axes[1,1].text(0,1, '\n'.join(so), va='top', fontsize=11)
    fig.suptitle('Dashboard Executivo — KPIs e Scores por Cluster (com So What?)')
    fig.text(0.01, 0.01, 'Fonte: metrics_ci.csv + features_engineered_regional.csv • Metodologia: split-aware anti-leakage, IC95%, Q-Q para normalidade', ha='left', va='bottom', fontsize=9)
    plt.tight_layout(rect=[0,0.04,1,0.95])
    plt.savefig(PRESENT/'v4_kpi_scores_dashboard.png', dpi=220)
    plt.close()
else:
    # fallback: apenas contagem por cluster
    cnt = pd.Series(labels).value_counts().sort_index()
    names = [name_map[int(c)] for c in cnt.index]
    plt.figure(figsize=(10,6))
    plt.bar(names, cnt.values, color=[name_color[n] for n in names])
    plt.title('Dashboard Executivo — Tamanho por Cluster (sem métricas de modelo)')
    plt.xticks(rotation=30, ha='right')
    plt.tight_layout(); plt.savefig(PRESENT/'v4_kpi_scores_dashboard.png', dpi=220); plt.close()

print('Final presentation visuals generated in', str(PRESENT))

