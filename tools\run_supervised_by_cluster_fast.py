from pathlib import Path
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, KFold, learning_curve
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from feature_filter import compute_clean_features
from cluster_business_naming import make_cluster_business_names

BASE = Path('.')
REPORTS = BASE/'reports'/'2025-08-15'
TABLES = REPORTS/'tables'
PLOTS = REPORTS/'plots'
TABLES.mkdir(parents=True, exist_ok=True)
PLOTS.mkdir(parents=True, exist_ok=True)

DATA = BASE/'data'/'processed'/'features_engineered_regional.csv'
df = pd.read_csv(DATA, low_memory=False)

# Target detection and anti-leakage feature selection
TARGET = 'valor' if 'valor' in df.columns else (df.select_dtypes('number').columns[-1])
base_targets = [TARGET,'valor','revenue','sales','y']

def _is_leak(c:str)->bool:
    cl=c.lower(); pats=[]
    for t in base_targets:
        t=t.lower(); pats += [t, f'_{t}', f'{t}_', f'sq_{t}', f'{t}_boxcox', f'{t}_log', f'{t}_log1p', f'{t}_sqrt', f'log_{t}', f'boxcox_{t}']
    return any(p in cl for p in pats)

# Hardened anti-leak: compute clean set with correlation + identifiers audit
clean_cols, audit_removed = compute_clean_features(df, TARGET, REPORTS)

# Clusters: try from 05; else proxy from territ signals
assign_path = TABLES/'cluster_assignments.csv'
if assign_path.exists():
    cl = pd.read_csv(assign_path)
    labels = cl['cluster'].values if 'cluster' in cl.columns else np.zeros(len(df), dtype=int)
    if len(labels) < len(df):
        labels = np.pad(labels, (0, len(df)-len(labels)), constant_values=labels[-1])
else:
    reg = [c for c in df.columns if 'REGIAO_CHILLI' in c]
    labels = np.argmax(df[reg].values, axis=1) if reg else np.zeros(len(df), dtype=int)

# Best model selection fallback
best_model_name = 'RF'

# Business names for clusters
assign_path = TABLES/'cluster_assignments.csv'
if assign_path.exists():
    cl = pd.read_csv(assign_path)
    labels = cl['cluster'].values if 'cluster' in cl.columns else np.zeros(len(df), dtype=int)
    if len(labels) < len(df):
        labels = np.pad(labels, (0, len(df)-len(labels)), constant_values=labels[-1])
else:
    reg = [c for c in df.columns if 'REGIAO_CHILLI' in c]
    labels = np.argmax(df[reg].values, axis=1) if reg else np.zeros(len(df), dtype=int)

names_df = make_cluster_business_names(df, labels, REPORTS)
name_map = {int(r.cluster): r.business_name for _, r in names_df.iterrows()}

def mk_estimator(name: str):
    if name in ['RF','GB','RandomForest']:
        base = RandomForestRegressor(n_estimators=120, random_state=42)
    elif name in ['SVM','SVR']:
        base = SVR(kernel='rbf')
    elif name in ['LR','Linear','LinearRegression']:
        base = LinearRegression()
    else:
        base = RandomForestRegressor(n_estimators=120, random_state=42)
    return Pipeline([('sc', StandardScaler(with_mean=False)), ('model', base)])

metrics_rows = []
all_preds = []
clusters = sorted(pd.Series(labels).dropna().unique())
for c_id in clusters:
    mask = (pd.Series(labels)==c_id).values
    dfc = df.loc[mask]
    if len(dfc) < 120:
        continue
    X = dfc[clean_cols].copy(); y = pd.to_numeric(dfc[TARGET], errors='coerce').fillna(0.0)
    # downsample large clusters for speed
    if len(X) > 8000:
        idx = np.random.RandomState(42).choice(len(X), 8000, replace=False)
        X = X.iloc[idx]; y = y.iloc[idx]
    Xtr, Xte, ytr, yte = train_test_split(X, y, test_size=0.2, random_state=42)
    est = mk_estimator(best_model_name)
    est.fit(Xtr, ytr)
    yp = est.predict(Xte)
    r2 = float(r2_score(yte, yp)); rmse = float(np.sqrt(mean_squared_error(yte, yp))); mae = float(mean_absolute_error(yte, yp))
    # Light bootstrap
    rng = np.random.RandomState(42)
    n=len(yte)
    B=50
    r2s=[]; rmses=[]; maes=[]
    for _ in range(B):
        idx = rng.choice(n, n, replace=True)
        yt = np.array(yte)[idx]; yp_b = np.array(yp)[idx]
        with np.errstate(all='ignore'):
            r2s.append(r2_score(yt, yp_b))
        rmses.append(float(np.sqrt(mean_squared_error(yt, yp_b))))
        maes.append(float(mean_absolute_error(yt, yp_b)))
    def ci(a):
        lo, hi = np.percentile(a, [2.5, 97.5]); return float(lo), float(hi)
    r2_lo,r2_hi=ci(np.asarray(r2s)); rmse_lo,rmse_hi=ci(np.asarray(rmses)); mae_lo,mae_hi=ci(np.asarray(maes))
    metrics_rows.append({'cluster':int(c_id),'n':int(len(dfc)),'r2':r2,'r2_lo':r2_lo,'r2_hi':r2_hi,'rmse':rmse,'rmse_lo':rmse_lo,'rmse_hi':rmse_hi,'mae':mae,'mae_lo':mae_lo,'mae_hi':mae_hi})
    all_preds.append(pd.DataFrame({'cluster':int(c_id),'y_true':yte.values,'y_pred':yp}))

if metrics_rows:
    mdf = pd.DataFrame(metrics_rows).sort_values('cluster')
    mdf.to_csv(TABLES/'supervised_by_cluster_metrics.csv', index=False)
    mdf.to_csv(TABLES/'supervised_by_cluster_metrics_ci.csv', index=False)
    # Plot with error bars
    fig, axes = plt.subplots(1,3, figsize=(16,5))
    x = mdf['cluster']
    labels_xtick = [name_map.get(int(c), f'Cluster {int(c)}') for c in x]
    axes[0].bar(x, mdf['r2'], yerr=[mdf['r2']-mdf['r2_lo'], mdf['r2_hi']-mdf['r2']], capsize=4)
    axes[0].set_title('R² por Cluster (95% CI) — modelos anti-vazamento\nFonte: features_engineered_regional.csv; Método: RF (downsample), 80-20 split')
    axes[0].set_xlabel('Cluster (nome de negócio)'); axes[0].set_xticks(x); axes[0].set_xticklabels(labels_xtick, rotation=30, ha='right'); axes[0].set_ylabel('R²')
    axes[1].bar(x, mdf['rmse'], yerr=[mdf['rmse']-mdf['rmse_lo'], mdf['rmse_hi']-mdf['rmse']], capsize=4)
    axes[1].set_title('RMSE por Cluster (95% CI) — menor é melhor') ; axes[1].set_xlabel('Cluster (nome de negócio)'); axes[1].set_xticks(x); axes[1].set_xticklabels(labels_xtick, rotation=30, ha='right')
    axes[2].bar(x, mdf['mae'], yerr=[mdf['mae']-mdf['mae_lo'], mdf['mae_hi']-mdf['mae']], capsize=4)
    axes[2].set_title('MAE por Cluster (95% CI) — menor é melhor'); axes[2].set_xlabel('Cluster (nome de negócio)'); axes[2].set_xticks(x); axes[2].set_xticklabels(labels_xtick, rotation=30, ha='right')
    plt.tight_layout(); plt.savefig(PLOTS/'supervised_by_cluster_performance.png', dpi=180); plt.close()

if all_preds:
    preds = pd.concat(all_preds, ignore_index=True)
    preds.to_csv(TABLES/'supervised_by_cluster_predictions.csv', index=False)
    plt.figure(figsize=(6,6))
    sns.scatterplot(data=preds, x='y_true', y='y_pred', hue='cluster', palette='tab10', s=12, alpha=0.6)
    lim = (min(preds['y_true'].min(), preds['y_pred'].min()), max(preds['y_true'].max(), preds['y_pred'].max()))
    plt.plot(lim, lim, 'k--', lw=1); plt.xlabel('Real'); plt.ylabel('Previsto'); plt.title('Predito vs Real por Cluster'); plt.tight_layout(); plt.savefig(PLOTS/'supervised_by_cluster_predictions.png', dpi=180); plt.close()

print('Fast supervised-by-cluster metrics generated')

