from pathlib import Path
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import KFold, learning_curve, train_test_split
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from feature_filter import compute_clean_features
from cluster_business_naming import make_cluster_business_names

BASE = Path('.')
REPORTS = BASE/'reports'/'2025-08-15'
TABLES = REPORTS/'tables'
PLOTS = REPORTS/'plots'
TABLES.mkdir(parents=True, exist_ok=True)
PLOTS.mkdir(parents=True, exist_ok=True)

DATA = BASE/'data'/'processed'/'features_engineered_regional.csv'
df = pd.read_csv(DATA, low_memory=False)

TARGET = 'valor' if 'valor' in df.columns else df.select_dtypes('number').columns[-1]
base_targets = [TARGET,'valor','revenue','sales','y']

def _is_leak(c:str)->bool:
    cl=c.lower(); pats=[]
    for t in base_targets:
        t=t.lower(); pats += [t, f'_{t}', f'{t}_', f'sq_{t}', f'{t}_boxcox', f'{t}_log', f'{t}_log1p', f'{t}_sqrt', f'log_{t}', f'boxcox_{t}']
    return any(p in cl for p in pats)

clean_cols, audit_removed = compute_clean_features(df, TARGET, REPORTS)

assign_path = TABLES/'cluster_assignments.csv'
if assign_path.exists():
    cl = pd.read_csv(assign_path)
    labels = cl['cluster'].values if 'cluster' in cl.columns else np.zeros(len(df), dtype=int)
    if len(labels) < len(df):
        labels = np.pad(labels, (0, len(df)-len(labels)), constant_values=labels[-1])
else:
    reg = [c for c in df.columns if 'REGIAO_CHILLI' in c]
    labels = np.argmax(df[reg].values, axis=1) if reg else np.zeros(len(df), dtype=int)

# business names
names_df = make_cluster_business_names(df, labels, REPORTS)
name_map = {int(r.cluster): r.business_name for _, r in names_df.iterrows()}

best_model_name = 'RF'

def mk_estimator(name: str):
    if name in ['RF','GB','RandomForest']:
        base = RandomForestRegressor(n_estimators=80, random_state=42)
    elif name in ['SVM','SVR']:
        base = SVR(kernel='rbf')
    elif name in ['LR','Linear','LinearRegression']:
        base = LinearRegression()
    else:
        base = RandomForestRegressor(n_estimators=80, random_state=42)
    return Pipeline([('sc', StandardScaler(with_mean=False)), ('model', base)])

clusters = sorted(pd.Series(labels).dropna().unique())
for c_id in clusters:
    mask = (pd.Series(labels)==c_id).values
    dfc = df.loc[mask]
    if len(dfc) < 200:
        continue
    X = dfc[clean_cols].copy(); y = pd.to_numeric(dfc[TARGET], errors='coerce').fillna(0.0)
    # Learning curves
    est = mk_estimator(best_model_name)
    cv = KFold(n_splits=4, shuffle=True, random_state=42)
    # Downsample to speed up learning_curve
    if len(X) > 6000:
        idx = np.random.RandomState(42).choice(len(X), 6000, replace=False)
        X_small = X.iloc[idx]; y_small = y.iloc[idx]
    else:
        X_small, y_small = X, y
    train_sizes, train_scores, val_scores = learning_curve(est, X_small, y_small, cv=cv, scoring='r2', train_sizes=np.linspace(0.2, 1.0, 5), n_jobs=1)
    tr_mean, tr_std = train_scores.mean(axis=1), train_scores.std(axis=1)
    va_mean, va_std = val_scores.mean(axis=1), val_scores.std(axis=1)
    plt.figure(figsize=(7,4.5))
    plt.plot(train_sizes, tr_mean, 'o-', label='Treino'); plt.fill_between(train_sizes, tr_mean-tr_std, tr_mean+tr_std, alpha=0.2)
    plt.plot(train_sizes, va_mean, 'o-', label='Validação'); plt.fill_between(train_sizes, va_mean-va_std, va_mean+va_std, alpha=0.2)
    plt.title(f"Curva de Aprendizado — {name_map.get(int(c_id), f'Cluster {int(c_id)}')}\nFonte: features_engineered_regional.csv; Modelo: RF (anti-vazamento)")
    plt.xlabel('Amostras de treino'); plt.ylabel('R²'); plt.legend(); plt.tight_layout()
    plt.savefig(PLOTS/f'learning_curve_cluster_{int(c_id)}.png', dpi=180); plt.close()

    # Residuals
    X_used, y_used = (X_small, y_small) if len(X) > 6000 else (X, y)
    Xtr, Xte, ytr, yte = train_test_split(X_used, y_used, test_size=0.2, random_state=42)
    est2 = mk_estimator(best_model_name); est2.fit(Xtr, ytr); yp = est2.predict(Xte)
    res = yte.values - yp
    plt.figure(figsize=(7,4.5)); sns.histplot(res, kde=True)
    plt.title(f"Distribuição de Resíduos — {name_map.get(int(c_id), f'Cluster {int(c_id)}')}\nNota: resíduos ~0 e sem viés forte indicam bom ajuste")
    plt.xlabel('Resíduo'); plt.ylabel('Frequência'); plt.tight_layout(); plt.savefig(PLOTS/f'residuals_hist_cluster_{int(c_id)}.png', dpi=180); plt.close()

    plt.figure(figsize=(7,4.5)); plt.scatter(yp, res, s=10, alpha=0.6); plt.axhline(0,color='k',lw=1)
    plt.title(f"Resíduos vs Previsto — {name_map.get(int(c_id), f'Cluster {int(c_id)}')}\nInsigh...vos")
    plt.xlabel('Previsto'); plt.ylabel('Resíduo'); plt.tight_layout(); plt.savefig(PLOTS/f'residuals_vs_fitted_cluster_{int(c_id)}.png', dpi=180); plt.close()

print('Advanced cluster visuals generated')

