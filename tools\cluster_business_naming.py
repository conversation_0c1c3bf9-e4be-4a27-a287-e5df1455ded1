from pathlib import Path
import pandas as pd
import numpy as np

# Heuristics to map cluster IDs into business-friendly names.
# Fix: clean region one-hot prefixes (Dim_Lojas.REGIAO_CHILLI_*) and default to "Cluster A/B/..." names.
# Also export a geographic summary to support business interpretation.

def _dominant(series: pd.Series):
    series = series.dropna().astype(str)
    vc = series[series.str.len() > 0].value_counts()
    return vc.index[0] if len(vc) else 'N/A'


def _clean_region_tag(raw: str) -> str:
    if raw is None:
        return 'N/A'
    s = str(raw)
    # Remove common prefixes and keep the last token after dots
    if 'REGIAO_CHILLI_' in s:
        s = s.split('REGIAO_CHILLI_')[-1]
    if '.' in s:
        s = s.split('.')[-1]
    s = s.replace('_', ' ').strip()
    up = s.upper()
    mapping = {
        'SAO PAULO': 'São Paulo', 'SÃO PAULO': 'São Paulo',
        'SUDESTE': 'Sudeste', 'SUL': 'Sul', 'NORTE': 'Norte', 'NORDESTE': 'Nordeste',
        'CENTRO-OESTE': 'Centro-Oeste', 'CENTRO OESTE': 'Centro-Oeste',
    }
    return mapping.get(up, s.title() if s else 'N/A')


def _index_to_letters(i: int) -> str:
    # 0->A, 1->B, ... 25->Z, 26->AA, etc.
    out = ''
    i = int(i)
    while True:
        i, r = divmod(i, 26)
        out = chr(ord('A') + r) + out
        if i == 0:
            break
        i -= 1  # Excel-like
    return out


def make_cluster_business_names(df: pd.DataFrame, labels: np.ndarray, reports_dir: Path):
    reports_dir.mkdir(parents=True, exist_ok=True)
    tables = reports_dir / 'tables'
    tables.mkdir(parents=True, exist_ok=True)

    clusters = sorted(pd.Series(labels).dropna().unique())
    cluster_index = {int(c): i for i, c in enumerate(clusters)}

    # Geography signals
    uf = df['UF'] if 'UF' in df.columns else pd.Series([np.nan]*len(df))
    reg_oh = [c for c in df.columns if 'REGIAO_CHILLI' in c]

    # Behavior/store signals (kept for description only)
    cand_amt = [c for c in df.columns if any(k in c.lower() for k in ['qtd','frete','desconto','preco_custo','ticket','valor_medio','ma_ticket','tx_'])]
    cand_freq = [c for c in df.columns if any(k in c.lower() for k in ['freq','recencia','txn','num_vale'])]

    rows = []
    geo_rows = []
    for cid in clusters:
        m = (pd.Series(labels) == cid).values
        d = df.loc[m]
        size = int(len(d))
        # Dominant UF and Region
        top_uf = _dominant(uf[m])
        reg_tag = None
        if reg_oh:
            sums = d[reg_oh].sum()
            if len(sums) > 0:
                idx = sums.sort_values(ascending=False).index[0]
                reg_tag = _clean_region_tag(idx)
        reg_tag = reg_tag or 'N/A'

        # Simple behavior summaries (for description only)
        try:
            avg_ticket = float(d[cand_amt].mean(axis=1).mean()) if cand_amt else np.nan
        except Exception:
            avg_ticket = np.nan
        try:
            freq_rate = float(d[cand_freq].mean(axis=1).mean()) if cand_freq else np.nan
        except Exception:
            freq_rate = np.nan

        # Business-friendly name: Cluster <Letter>
        letter = _index_to_letters(cluster_index[int(cid)])
        name = f'Cluster {letter}'

        # Description enriches geographic context
        desc = (
            f"UF dominante: {top_uf}; Região dominante: {reg_tag}; tamanho: {size}. "
            f"Ticket proxy: {avg_ticket:.3f} | frequência proxy: {freq_rate:.3f}"
        )
        rows.append({'cluster': int(cid), 'business_name': name, 'description': desc})

        # Geographic summary (top 3 UFs + shares)
        uf_counts = uf[m].dropna().astype(str).value_counts()
        total = int(uf_counts.sum()) if uf_counts.size else 0
        top3 = []
        if total > 0:
            for u, n in uf_counts.head(3).items():
                pct = 100.0 * n / total
                top3.append(f"{u}:{pct:.1f}%")
        geo_rows.append({
            'cluster': int(cid), 'cluster_name': name,
            'size': size, 'top_uf': top_uf, 'top_region': reg_tag,
            'top3_ufs': ' | '.join(top3) if top3 else '',
        })

    out = pd.DataFrame(rows).sort_values('cluster')
    out.to_csv(tables/'cluster_business_names.csv', index=False)

    geo = pd.DataFrame(geo_rows).sort_values('cluster')
    geo.to_csv(tables/'cluster_geography_summary.csv', index=False)

    return out

