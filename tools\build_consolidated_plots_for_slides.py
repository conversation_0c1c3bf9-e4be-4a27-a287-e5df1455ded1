from pathlib import Path
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import matplotlib as mpl
from sklearn.model_selection import KFold, learning_curve, train_test_split
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
from sklearn.linear_model import LinearRegression
from feature_filter import compute_clean_features
from cluster_business_naming import make_cluster_business_names

# Optional for QQ
try:
    from scipy import stats
except Exception:
    stats = None

# Base paths
BASE = Path('.')
REPORTS = BASE/'reports'/'2025-08-15'
TABLES = REPORTS/'tables'
PLOTS = REPORTS/'plots'
PRESENT = PLOTS/'presentation'
SLIDES = PRESENT/'slides'
SLIDES.mkdir(parents=True, exist_ok=True)

# Data
DATA = BASE/'data'/'processed'/'features_engineered_regional.csv'
df = pd.read_csv(DATA, low_memory=False)

# Target and features (anti-vazamento helpers reused)
TARGET = 'valor' if 'valor' in df.columns else df.select_dtypes('number').columns[-1]
clean_cols, audit_removed = compute_clean_features(df, TARGET, REPORTS)

# Cluster labels and names
assign_path = TABLES/'cluster_assignments.csv'
if assign_path.exists():
    lab = pd.read_csv(assign_path)
    labels = lab['cluster'].values if 'cluster' in lab.columns else np.zeros(len(df), dtype=int)
    if len(labels) < len(df):
        labels = np.pad(labels, (0, len(df)-len(labels)), constant_values=labels[-1])
else:
    reg = [c for c in df.columns if 'REGIAO_CHILLI' in c]
    labels = np.argmax(df[reg].values, axis=1) if reg else np.zeros(len(df), dtype=int)

names_df = make_cluster_business_names(df, labels, REPORTS)
name_map = {int(r.cluster): r.business_name for _, r in names_df.iterrows()}

# Consistent palette (tab20)
palette = mpl.colormaps.get_cmap('tab20') if hasattr(mpl, 'colormaps') else mpl.cm.get_cmap('tab20')
clusters = sorted(pd.Series(labels).dropna().unique().astype(int))
cluster_colors = {int(c): mpl.colors.to_hex(palette(i % 20)) for i, c in enumerate(clusters)}

# Estimator factory
BEST_MODEL = 'RF'

def mk_estimator(name: str):
    if name in ['RF','GB','RandomForest']:
        base = RandomForestRegressor(n_estimators=100, random_state=42)
    elif name in ['SVM','SVR']:
        base = SVR(kernel='rbf')
    elif name in ['LR','Linear','LinearRegression']:
        base = LinearRegression()
    else:
        base = RandomForestRegressor(n_estimators=100, random_state=42)
    return Pipeline([('sc', StandardScaler(with_mean=False)), ('model', base)])

# Collect per-cluster curves and residuals
curves = []  # list of dicts: {'cluster', 'name', 'sizes', 'train_mean', 'val_mean'}
residuals_bucket = {}  # cluster -> dict(y_pred=, resid=)

rng = np.random.RandomState(42)

for c in clusters:
    mask = (pd.Series(labels) == c).values
    dfc = df.loc[mask]
    if len(dfc) < 200:
        continue
    X = dfc[clean_cols].copy()
    y = pd.to_numeric(dfc[TARGET], errors='coerce').fillna(0.0)

    # Learning curve with capped sample size to speed up
    est = mk_estimator(BEST_MODEL)
    cv = KFold(n_splits=4, shuffle=True, random_state=42)
    if len(X) > 6000:
        idx = rng.choice(len(X), 6000, replace=False)
        X_small = X.iloc[idx]; y_small = y.iloc[idx]
    else:
        X_small, y_small = X, y

    sizes, tr_scores, va_scores = learning_curve(
        est, X_small, y_small,
        cv=cv, scoring='r2', train_sizes=np.linspace(0.2, 1.0, 5), n_jobs=1
    )
    curves.append({
        'cluster': int(c),
        'name': name_map.get(int(c), f'Cluster {int(c)}'),
        'sizes': sizes,
        'train_mean': tr_scores.mean(axis=1),
        'train_std': tr_scores.std(axis=1),
        'val_mean': va_scores.mean(axis=1),
        'val_std': va_scores.std(axis=1),
    })

    # Residual diagnostics
    X_used, y_used = (X_small, y_small) if len(X) > 6000 else (X, y)
    from sklearn.model_selection import train_test_split as _tts
    Xtr, Xte, ytr, yte = _tts(X_used, y_used, test_size=0.2, random_state=42)
    est2 = mk_estimator(BEST_MODEL); est2.fit(Xtr, ytr); y_pred = est2.predict(Xte)
    resid = yte.values - y_pred
    residuals_bucket[int(c)] = {
        'name': name_map.get(int(c), f'Cluster {int(c)}'),
        'y_pred': y_pred,
        'resid': resid
    }

# 1) Consolidated learning curves (overlay)
if curves:
    # v1: linhas finas, tamanho médio
    for ver, fig_size, lw in [(1, (14, 8), 2.0), (2, (18, 10), 2.5), (3, (22, 12), 3.0)]:
        plt.figure(figsize=fig_size)
        for cdat in curves:
            c = cdat['cluster']; col = cluster_colors[c]
            sizes = cdat['sizes']
            plt.plot(sizes, cdat['train_mean'], color=col, linestyle='-', linewidth=lw, label=f"{cdat['name']} — Treino")
            plt.plot(sizes, cdat['val_mean'], color=col, linestyle='--', linewidth=lw)
        plt.title('Curvas de Aprendizado (todos os clusters) — R² vs amostras')
        plt.xlabel('Amostras de treino')
        plt.ylabel('R²')
        plt.grid(True, alpha=0.3)
        # Legend: one entry por cluster (Treino/Validação explicado na nota)
        handles = [plt.Line2D([0],[0], color=cluster_colors[cd['cluster']], lw=lw) for cd in curves]
        labels_leg = [cd['name'] for cd in curves]
        leg = plt.legend(handles, labels_leg, title='Clusters', fontsize=10, title_fontsize=11, ncols=2, frameon=True, loc='lower right')
        # Nota de metodologia
        plt.gcf().text(0.01, 0.01, 'Metodologia: RF (pipeline com StandardScaler); Validação: KFold(4); Linhas contínuas = Treino, tracejadas = Validação', fontsize=9)
# 0. Executive clustering visuals (distribuição, silhouette, sumário)
try:
    # Distribuição por cluster (contagem e %)
    lab_series = pd.Series(labels, name='cluster')
    dist = lab_series.value_counts().sort_index()
    perc = (dist / dist.sum() * 100).round(1)
    names_exec = [name_map.get(int(c), f'Cluster {int(c)}') for c in dist.index]
    for ver, figsize in [(1, (10,6)), (2, (12,7))]:
        fig, ax = plt.subplots(figsize=figsize)
        bars = ax.bar(names_exec, dist.values, color=[cluster_colors[int(c)] for c in dist.index])
        ax.set_title('Distribuição de Clusters (contagem)'); ax.set_ylabel('N casos'); ax.tick_params(axis='x', rotation=30)
        for b, p in zip(bars, perc.values):
            ax.text(b.get_x()+b.get_width()/2, b.get_height()*1.01, f'{p:.1f}%', ha='center', va='bottom', fontsize=9)
        ax.grid(True, axis='y', alpha=0.3)
        plt.tight_layout(); plt.savefig(SLIDES/f'clusters_distribution_v{ver}.png', dpi=320); plt.close()

    # Silhouette (amostra para performance)
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import silhouette_score, silhouette_samples
    X_all = df[clean_cols].fillna(0.0)
    if len(X_all) > 6000:
        idx = np.random.RandomState(42).choice(len(X_all), 6000, replace=False)
        Xs = X_all.iloc[idx]; labs = pd.Series(labels).iloc[idx].values
    else:
        Xs, labs = X_all, labels
    Xsc = StandardScaler(with_mean=False).fit_transform(Xs)
    sil = float(silhouette_score(Xsc, labs)) if len(np.unique(labs))>1 else np.nan
    sil_samp = silhouette_samples(Xsc, labs) if len(np.unique(labs))>1 else np.array([])
    # Boxplot de silhouette por cluster
    if sil_samp.size>0:
        fig, ax = plt.subplots(figsize=(10,6))
        data = [sil_samp[pd.Series(labs)==c] for c in sorted(np.unique(labs))]
        bp = ax.boxplot(data, labels=[name_map.get(int(c)) for c in sorted(np.unique(labs))], patch_artist=True)
        for patch, c in zip(bp['boxes'], [cluster_colors[int(c)] for c in sorted(np.unique(labs))]):
            patch.set_facecolor(c); patch.set_alpha(0.7)
        ax.axhline(0, color='k', lw=1, ls='--'); ax.set_title(f'Silhouette por Cluster (média global = {sil:.3f})')
        ax.set_ylabel('Coeficiente de Silhouette'); ax.grid(True, axis='y', alpha=0.3)
        plt.tight_layout(); plt.savefig(SLIDES/'clusters_silhouette_box_v1.png', dpi=320); plt.close()

    # Sumário de características por cluster (médias normalizadas das top features)
    corr_with_target = df[clean_cols].corrwith(pd.to_numeric(df[TARGET], errors='coerce')).abs().sort_values(ascending=False)
    top10 = corr_with_target.head(10).index.tolist() if corr_with_target.notna().any() else clean_cols[:10]
    means = df.groupby(pd.Series(labels, name='cluster'))[top10].mean()
    means_norm = (means - means.min()) / (means.max() - means.min() + 1e-9)
    plt.figure(figsize=(max(12, len(top10)*0.9), 6))
    sns.heatmap(means_norm, annot=False, cmap='YlGnBu', cbar_kws={'label':'Média normalizada'})
    plt.title('Resumo de Características por Cluster — Top 10 variáveis'); plt.xlabel('Variáveis'); plt.ylabel('Cluster')
    plt.tight_layout(); plt.savefig(SLIDES/'clusters_characteristics_heatmap_v1.png', dpi=320); plt.close()
except Exception as e:
    print('Executive clustering visuals block skipped:', e)

# 2) Unified QQ and residual diagnostics
if residuals_bucket:
    # Build combined DataFrame for convenience
    rows = []
    for c, d in residuals_bucket.items():
        n = len(d['y_pred'])
        rows.append(pd.DataFrame({'cluster':c, 'name': d['name'], 'y_pred': d['y_pred'], 'resid': d['resid']}))
    all_res = pd.concat(rows, ignore_index=True)

    # Residuals vs Fitted (overlay and grid)
    for ver, fig_size, s in [(1, (12, 8), 10), (2, (16, 10), 12), (3, (20, 12), 14)]:
        plt.figure(figsize=fig_size)
        for c in clusters:
            sub = all_res[all_res['cluster']==c]
            if sub.empty: continue
            plt.scatter(sub['y_pred'], sub['resid'], s=s, alpha=0.5, color=cluster_colors[c], label=name_map.get(int(c)))
        plt.axhline(0, color='k', lw=1)
        plt.xlabel('Previsto'); plt.ylabel('Resíduo')
        plt.title('Resíduos vs Previsto — Todos os Clusters')
        plt.grid(True, alpha=0.3)
        plt.legend(title='Clusters', fontsize=10, title_fontsize=11, ncols=2, frameon=True)
        plt.tight_layout()
        plt.savefig(SLIDES/f'resid_vs_fitted_overlay_v{ver}.png', dpi=320)
        plt.close()

    # Grid version
    nC = len(clusters); cols = min(4, nC); rows_n = (nC + cols - 1)//cols
    fig, axes = plt.subplots(rows_n, cols, figsize=(5*cols, 4*rows_n), sharey=True)
    axes = np.atleast_1d(axes).ravel()
    for i, c in enumerate(clusters):
        ax = axes[i]
        sub = all_res[all_res['cluster']==c]
        if len(sub)==0:
            ax.axis('off'); continue
        ax.scatter(sub['y_pred'], sub['resid'], s=10, alpha=0.5, color=cluster_colors[c])
        ax.axhline(0, color='k', lw=1, ls='--')
        ax.set_title(name_map.get(int(c)))
        ax.set_xlabel('Previsto'); ax.set_ylabel('Resíduo')
        ax.grid(True, alpha=0.2)
    for j in range(i+1, len(axes)):
        axes[j].axis('off')
    plt.tight_layout()
    plt.savefig(SLIDES/'resid_vs_fitted_grid_v1.png', dpi=320)
    plt.close()

    # Scale-Location (sqrt(|resid padronizado|) vs previsto) overlay
    def std_resid(r):
        s = np.std(r, ddof=1)
        if s <= 0: return r
        return (r - np.mean(r)) / s
    for ver, fig_size, s in [(1, (12, 8), 10), (2, (16, 10), 12)]:
        plt.figure(figsize=fig_size)
        for c in clusters:
            sub = all_res[all_res['cluster']==c]
            if sub.empty: continue
            rstd = std_resid(sub['resid'].values)
            plt.scatter(sub['y_pred'], np.sqrt(np.abs(rstd)), s=s, alpha=0.5, color=cluster_colors[c], label=name_map.get(int(c)))
        plt.xlabel('Previsto'); plt.ylabel('sqrt(|Resíduo padronizado|)')
        plt.title('Scale-Location — Todos os Clusters')
        plt.grid(True, alpha=0.3)
        plt.legend(title='Clusters', fontsize=10, title_fontsize=11, ncols=2, frameon=True)
        plt.tight_layout()
        plt.savefig(SLIDES/f'scale_location_overlay_v{ver}.png', dpi=320)
        plt.close()

    # QQ plots: overlay and grid (if scipy available)
    if stats is not None:
        # Overlay: each cluster curve
        for ver, fig_size in [(1, (10, 10)), (2, (12, 12))]:
            plt.figure(figsize=fig_size)
            for c in clusters:
                sub = all_res[all_res['cluster']==c]
                if len(sub) < 20: continue
                (osm, osr), (slope, interc, r) = stats.probplot(sub['resid'].values, dist='norm')
                plt.plot(osm, osr, 'o', ms=4, alpha=0.6, color=cluster_colors[c], label=name_map.get(int(c)))
            # Reference line
            lims = plt.axis()
            lo = min(lims[0], lims[2]); hi = max(lims[1], lims[3])
            plt.plot([lo, hi], [lo, hi], 'k--', lw=1)
            plt.xlabel('Quantis teóricos'); plt.ylabel('Quantis dos resíduos')
            plt.title('Q-Q Plot de Resíduos — Todos os Clusters (sobreposto)')
            plt.grid(True, alpha=0.3)
            plt.legend(title='Clusters', ncols=2, fontsize=10, title_fontsize=11, frameon=True)
            plt.tight_layout()
            plt.savefig(SLIDES/f'qq_overlay_v{ver}.png', dpi=320)
            plt.close()

        # Grid
        fig, axes = plt.subplots(rows_n, cols, figsize=(5*cols, 4*rows_n))
        axes = np.atleast_1d(axes).ravel()
        for i, c in enumerate(clusters):
            ax = axes[i]
            sub = all_res[all_res['cluster']==c]
            if len(sub) < 20:
                ax.text(0.2,0.5,'Amostra insuficiente', transform=ax.transAxes)
                ax.axis('off'); continue
            stats.probplot(sub['resid'].values, dist='norm', plot=ax)
            ax.set_title(name_map.get(int(c)))
            ax.grid(True, alpha=0.2)
        for j in range(i+1, len(axes)):
            axes[j].axis('off')
        plt.tight_layout()
        plt.savefig(SLIDES/'qq_grid_v1.png', dpi=320)
        plt.close()
# 2.5) Supervised global diagnostics (modelo único, estilo consistente)
try:
    # Dados globais limpos e amostragem controlada
    X_all = df[clean_cols].copy()
    y_all = pd.to_numeric(df[TARGET], errors='coerce').fillna(0.0)
    if len(X_all) > 8000:
        idx = rng.choice(len(X_all), 8000, replace=False)
        X_g = X_all.iloc[idx]; y_g = y_all.iloc[idx]
    else:
        X_g, y_g = X_all, y_all

    est_g = mk_estimator(BEST_MODEL)

    # Learning curve global
    sizes_g, tr_g, va_g = learning_curve(est_g, X_g, y_g, cv=KFold(n_splits=4, shuffle=True, random_state=42),
                                         scoring='r2', train_sizes=np.linspace(0.2, 1.0, 6), n_jobs=1)
    for ver, fig_size, lw in [(1, (12,7), 2.2), (2, (16,9), 2.6)]:
        plt.figure(figsize=fig_size)
        plt.plot(sizes_g, tr_g.mean(axis=1), label='Treino', color='#1f77b4', lw=lw)
        plt.plot(sizes_g, va_g.mean(axis=1), label='Validação', color='#1f77b4', lw=lw, ls='--')
        plt.title('Modelo Supervisionado — Curva de Aprendizado (R²)')
        plt.xlabel('Amostras de treino'); plt.ylabel('R²'); plt.grid(True, alpha=0.3)
        plt.legend(); plt.tight_layout(); plt.savefig(SLIDES/f'supervised_global_learning_curves_v{ver}.png', dpi=320); plt.close()

    # Holdout para resíduos e importâncias
    Xtr_g, Xte_g, ytr_g, yte_g = train_test_split(X_g, y_g, test_size=0.2, random_state=42)
    est_g2 = mk_estimator(BEST_MODEL); est_g2.fit(Xtr_g, ytr_g); yp_g = est_g2.predict(Xte_g)

    # Predito vs Real
    plt.figure(figsize=(7,6))
    plt.scatter(yte_g, yp_g, s=18, alpha=0.6, color='#1f77b4')
    mn, mx = min(yte_g.min(), yp_g.min()), max(yte_g.max(), yp_g.max())
    plt.plot([mn,mx],[mn,mx],'k--',lw=1)
    from sklearn.metrics import r2_score, mean_squared_error
    r2g = r2_score(yte_g, yp_g); rmseg = float(np.sqrt(mean_squared_error(yte_g, yp_g)))
    plt.title('Predito vs Real — Modelo Supervisionado')
    plt.xlabel('Valores Reais'); plt.ylabel('Valores Preditos')
    plt.gcf().text(0.02,0.02,f'R²={r2g:.3f} | RMSE={rmseg:.1f}',fontsize=10)
    plt.grid(True, alpha=0.3); plt.tight_layout(); plt.savefig(SLIDES/'supervised_pred_vs_actual_v1.png', dpi=320); plt.close()

    # Resíduos e QQ
    resid_g = yte_g.values - yp_g
    plt.figure(figsize=(7,5)); plt.scatter(yp_g, resid_g, s=16, alpha=0.6, color='#2ca02c')
    plt.axhline(0, color='k', ls='--', lw=1); plt.xlabel('Previsto'); plt.ylabel('Resíduo'); plt.title('Resíduos vs Previsto — Modelo Supervisionado')
    plt.grid(True, alpha=0.3); plt.tight_layout(); plt.savefig(SLIDES/'supervised_residuals_v1.png', dpi=320); plt.close()

    if stats is not None and len(resid_g) > 20:
        plt.figure(figsize=(6,6)); stats.probplot(resid_g, dist='norm', plot=plt.gca())
        plt.title('Q-Q Plot de Resíduos — Modelo Supervisionado'); plt.grid(True, alpha=0.2)
        plt.tight_layout(); plt.savefig(SLIDES/'supervised_qq_v1.png', dpi=320); plt.close()

    # Importância de variáveis (RF)
    try:
        model_core = getattr(est_g2, 'named_steps', {}).get('model', None)
        if model_core is not None and hasattr(model_core, 'feature_importances_'):
            imp = pd.Series(model_core.feature_importances_, index=X_g.columns).sort_values(ascending=False).head(20)
            plt.figure(figsize=(10,8)); imp[::-1].plot(kind='barh', color='#ff7f0e')
            plt.title('Importância das Variáveis — Top 20 (Modelo RF)')
            plt.xlabel('Importância relativa'); plt.tight_layout(); plt.savefig(SLIDES/'supervised_feature_importance_v1.png', dpi=320); plt.close()
    except Exception as e:
        print('Feature importance skipped:', e)

    # Métricas por fold (KFold)
    from sklearn.model_selection import cross_validate
    scoring = {'r2':'r2','rmse':'neg_root_mean_squared_error','mae':'neg_mean_absolute_error'}
    cvres = cross_validate(est_g, X_g, y_g, cv=KFold(n_splits=5, shuffle=True, random_state=42), scoring=scoring, n_jobs=1)
    r2v = cvres['test_r2']; rmsev = -cvres['test_rmse']; maev = -cvres['test_mae']
    fig, axes = plt.subplots(1,3, figsize=(16,5))
    for ax, arr, title in zip(axes, [r2v, rmsev, maev], ['R²', 'RMSE', 'MAE']):
        ax.boxplot(arr, patch_artist=True, boxprops=dict(facecolor='#AEC7E8'))
        ax.set_title(f'{title} por Fold'); ax.set_xticks([])
    plt.suptitle('Validação Cruzada — Métricas por Fold (Modelo Supervisionado)')
    plt.tight_layout(rect=[0,0,1,0.95]); plt.savefig(SLIDES/'supervised_metrics_cv_box_v1.png', dpi=320); plt.close()

    # Barras com média±dp
    means = [np.mean(r2v), np.mean(rmsev), np.mean(maev)]; stds = [np.std(r2v), np.std(rmsev), np.std(maev)]
    labels_m = ['R²', 'RMSE', 'MAE']
    plt.figure(figsize=(8,6)); bars = plt.bar(labels_m, means, yerr=stds, capsize=5, color=['#1f77b4','#ffbb78','#98df8a'])
    for b,m in zip(bars, means): plt.text(b.get_x()+b.get_width()/2, b.get_height()*1.01, f'{m:.3f}', ha='center', va='bottom')
    plt.title('Média e Desvio-Padrão — Validação Cruzada'); plt.grid(True, axis='y', alpha=0.3); plt.tight_layout(); plt.savefig(SLIDES/'supervised_metrics_cv_bars_v1.png', dpi=320); plt.close()

    # Comparação (clusters x supervised): painel lado a lado
    met_path = TABLES/'supervised_by_cluster_metrics_ci.csv'
    if met_path.exists():
        mdf = pd.read_csv(met_path)
        # Painel com tamanhos (n) e R² por cluster
        fig, axes = plt.subplots(1,2, figsize=(18,7))
        # Esquerda: tamanho por cluster
        mdf = mdf.sort_values('cluster')
        names = [name_map.get(int(c), f'Cluster {int(c)}') for c in mdf['cluster']]
        axes[0].bar(names, mdf['n'], color=[cluster_colors[int(c)] for c in mdf['cluster']])
        axes[0].set_title('Tamanho por Cluster'); axes[0].set_ylabel('N casos'); axes[0].tick_params(axis='x', rotation=30)
        # Direita: R² por cluster (com barras de erro)
        axes[1].bar(names, mdf['r2'], yerr=[mdf['r2']-mdf['r2_lo'], mdf['r2_hi']-mdf['r2']], capsize=4, color=[cluster_colors[int(c)] for c in mdf['cluster']])
        axes[1].set_title('R² por Cluster (95% CI)'); axes[1].set_ylabel('R²'); axes[1].tick_params(axis='x', rotation=30)
        for ax in axes: ax.grid(True, axis='y', alpha=0.3)
        plt.suptitle('Comparação Segmentação (Clusters) x Desempenho Supervisionado', fontweight='bold')
        plt.tight_layout(rect=[0,0,1,0.94]); plt.savefig(SLIDES/'model_comparison_clusters_vs_supervised_v1.png', dpi=320); plt.close()

except Exception as e:
    print('Supervised global diagnostics block skipped:', e)


# 3) Territorial map optimized (Plotly-based export with size/scale variants)
import json, requests
import plotly.graph_objects as go
import plotly.io as pio

# Prepare aggregation at UF level (fallback to REGIAO)
if 'UF' in df.columns and df['UF'].notna().sum() > 0:
    key_series = df['UF'].astype(str)
    key_name = 'UF'
else:
    region_cols = [c for c in df.columns if c.startswith('Dim_Lojas.REGIAO_CHILLI_')]
    if not region_cols:
        key_series = pd.Series(['N/A']*len(df), name='UF'); key_name = 'UF'
    else:
        idx = np.argmax(df[region_cols].values, axis=1)
        def col_to_region(col): return col.split('REGIAO_CHILLI_')[-1].strip()
        regions = [col_to_region(region_cols[i]) for i in idx]
        # Map regions to UFs (expansion heuristic)
        reg_to_ufs = {
            'CENTRO-OESTE': ['DF','GO','MT','MS'], 'NORDESTE': ['AL','BA','CE','MA','PB','PE','PI','RN','SE'],
            'NORTE': ['AC','AP','AM','PA','RO','RR','TO'], 'SUDESTE': ['ES','MG','RJ','SP'], 'SUL': ['PR','RS','SC'], 'SÃO PAULO': ['SP']
        }
        exp = []
        for reg, lab_c in zip(regions, labels):
            for uf in reg_to_ufs.get(reg, ['SP']):
                exp.append({'UF': uf, 'cluster': int(lab_c)})
        exp_df = pd.DataFrame(exp)
        key_series = exp_df['UF']; df = exp_df  # override for aggregation below
        labels = exp_df['cluster'].values
        key_name = 'UF'

# Geojson
raw = requests.get('https://raw.githubusercontent.com/codeforamerica/click_that_hood/master/public/data/brazil-states.geojson').text
geojson = json.loads(raw)

# Aggregation
agg = pd.DataFrame({key_name: key_series, 'cluster': labels}).groupby([key_name,'cluster']).size().rename('count').reset_index()
pivot = agg.pivot(index=key_name, columns='cluster', values='count').fillna(0)
pivot['total'] = pivot.sum(axis=1)
for c in clusters:
    pivot[f'share_{int(c)}'] = pivot[c] / pivot['total'].replace({0:np.nan})

# Dominant cluster per UF
uf_df = pd.DataFrame({'UF': pivot.index})
uf_df['cluster_dom'] = [int(pivot.loc[u, clusters].astype(float).idxmax()) if u in pivot.index else clusters[0] for u in uf_df['UF']]
uf_df['business_name'] = uf_df['cluster_dom'].map(name_map)

# Marker sizes based on counts
uf_df['n_total'] = pivot.loc[uf_df['UF'], 'total'].values

# Location mapping: name property to UF
name_to_uf = {
    'Acre':'AC','Alagoas':'AL','Amapá':'AP','Amazonas':'AM','Bahia':'BA','Ceará':'CE','Distrito Federal':'DF',
    'Espírito Santo':'ES','Goiás':'GO','Maranhão':'MA','Mato Grosso':'MT','Mato Grosso do Sul':'MS','Minas Gerais':'MG',
    'Pará':'PA','Paraíba':'PB','Paraná':'PR','Pernambuco':'PE','Piauí':'PI','Rio de Janeiro':'RJ','Rio Grande do Norte':'RN',
    'Rio Grande do Sul':'RS','Rondônia':'RO','Roraima':'RR','Santa Catarina':'SC','São Paulo':'SP','Sergipe':'SE','Tocantins':'TO'}

# Build figure factory functions

def make_map_simple(width_px:int, height_px:int, version:int):
    # Prepara mapeamento categórico: cluster -> cor
    cid_list = sorted(set(uf_df['cluster_dom'].astype(int).tolist()))
    zmin, zmax = (min(cid_list), max(cid_list)) if cid_list else (0, 1)
    # Colorscale discreto posicionado em 0..1
    denom = (zmax - zmin) if (zmax - zmin) != 0 else 1
    colorscale = []
    for i, c in enumerate(sorted(cid_list)):
        pos = (c - zmin) / denom
        col = cluster_colors[int(c)]
        colorscale.append([pos, col])
        colorscale.append([pos, col])

    # z por estado (cluster dominante)
    state_names = [f['properties']['name'] for f in geojson['features']]
    uf_for_state = [name_to_uf.get(n, '') for n in state_names]
    dom_by_uf = uf_df.set_index('UF')['cluster_dom'].to_dict()
    zvals = [dom_by_uf.get(u, zmin) for u in uf_for_state]

    fig = go.Figure()
    fig.add_trace(go.Choropleth(
        geojson=geojson,
        locations=state_names,
        z=zvals,
        zmin=zmin, zmax=zmax,
        featureidkey='properties.name',
        colorscale=colorscale, showscale=False,
        marker_line_color='white', marker_line_width=0.8,
        hovertemplate='<b>%{location}</b><extra></extra>'
    ))

    # Centroides e rótulos UF
    centroids = {
        'AC':(-70.0,-9.0),'AL':(-36.6,-9.6),'AP':(-51.8,1.4),'AM':(-64.7,-3.4),'BA':(-41.7,-12.5),'CE':(-39.6,-5.3),'DF':(-47.9,-15.8),'ES':(-40.3,-19.5),'GO':(-49.3,-15.9),
        'MA':(-45.2,-5.0),'MT':(-56.1,-12.6),'MS':(-54.2,-20.5),'MG':(-44.6,-18.5),'PA':(-52.8,-3.8),'PB':(-36.7,-7.2),'PR':(-51.5,-24.5),'PE':(-37.9,-8.4),'PI':(-42.7,-7.0),
        'RJ':(-42.6,-22.1),'RN':(-36.8,-5.7),'RS':(-53.2,-30.0),'RO':(-63.4,-10.8),'RR':(-61.3,2.0),'SC':(-50.9,-27.2),'SP':(-48.0,-22.1),'SE':(-37.4,-10.5),'TO':(-48.3,-10.2)
    }
    pos = uf_df['UF'].map(centroids)
    lon = [p[0] for p in pos]; lat = [p[1] for p in pos]

    # Sobrepor rótulos UF para legibilidade executiva
    fig.add_trace(go.Scattergeo(
        lon=lon, lat=lat,
        mode='text', text=uf_df['UF'].values,
        textfont=dict(size=22, color='white'),
        name='UF'
    ))

    # Legenda de clusters (marcadores fictícios)
    for c in sorted(cid_list):
        fig.add_trace(go.Scattergeo(
            lon=[-32.5], lat=[-35 - 1.5*c],
            mode='markers', marker=dict(size=18, color=cluster_colors[int(c)], line=dict(color='white', width=0.5)),
            name=name_map.get(int(c), f'Cluster {c}')
        ))

    # Não usar topojson externo; ajustar ao GeoJSON local
    fig.update_geos(fitbounds="locations", showcountries=False, showcoastlines=False, projection_type='mercator', visible=True)
    fig.update_layout(
        width=width_px, height=height_px,
        margin=dict(l=20,r=20,t=40,b=20),
        legend=dict(title='Clusters', font=dict(size=20), orientation='h', yanchor='bottom', y=0.02, xanchor='right', x=0.98),
        title=dict(text='Distribuição de Clusters por UF (dominância por estado)', x=0.5, xanchor='center', font=dict(size=28))
    )
    # Nota executiva simples
    fig.add_annotation(
        text='Cores = cluster dominante por estado; rótulos = siglas de UF.',
        xref='paper', yref='paper', x=0.01, y=0.01, showarrow=False, font=dict(size=16, color='rgba(60,60,60,0.95)')
    )
    out = SLIDES/f'territorial_map_simple_v{version}.png'
    pio.write_image(fig, str(out), scale=2)

# Gerar variantes simplificadas (sem métricas)
_simple_variants = [
    (2000, 1800, 1),
    (2600, 2200, 2),
    (3000, 2500, 3),
]
for w, h, ver in _simple_variants:
    try:
        make_map_simple(w, h, ver)
    except Exception as e:
        print('Simple map generation failed:', ver, e)

print('Consolidated slide visuals generated into', SLIDES)

