from pathlib import Path
import pandas as pd
import numpy as np
import geopandas as gpd
import matplotlib.pyplot as plt

BASE = Path('.')
REPORTS = BASE/'reports'/'2025-08-15'
TABLES = REPORTS/'tables'
PLOTS = REPORTS/'plots'
TABLES.mkdir(parents=True, exist_ok=True)
PLOTS.mkdir(parents=True, exist_ok=True)

DATA = BASE/'data'/'processed'/'features_engineered_regional.csv'
df = pd.read_csv(DATA, low_memory=False)

# Load clusters
assign_path = TABLES/'cluster_assignments.csv'
if assign_path.exists():
    cl = pd.read_csv(assign_path)
    if 'cluster' in cl.columns:
        labels = cl['cluster'].values
        if len(labels) < len(df):
            labels = np.pad(labels, (0, len(df)-len(labels)), constant_values=labels[-1])
    else:
        labels = np.zeros(len(df), dtype=int)
else:
    # proxy clusters: REGIAO_CHILLI one-hots or zeros
    oh = [c for c in df.columns if 'REGIAO_CHILLI' in c]
    if oh:
        labels = np.argmax(df[oh].values, axis=1)
    else:
        labels = np.zeros(len(df), dtype=int)

if 'UF' in df.columns:
    uf = df['UF'].astype(str)
else:
    oh_uf = [c for c in df.columns if c.startswith('UF_')]
    if oh_uf:
        idx = np.argmax(df[oh_uf].values, axis=1)
        uf = pd.Series([oh_uf[i].replace('UF_','') for i in idx])
    else:
        raise SystemExit('UF not available for choropleth')

# Dominant cluster per UF
summary = (
    pd.DataFrame({'UF': uf, 'cluster': labels})
      .groupby('UF')['cluster'].agg(lambda s: s.value_counts().idxmax()).reset_index()
)
counts = uf.value_counts().rename('count').reset_index().rename(columns={'index':'UF'})
summary = summary.merge(counts, on='UF', how='left')

# Load GeoJSON into GeoDataFrame
url = 'https://raw.githubusercontent.com/codeforamerica/click_that_hood/master/public/data/brazil-states.geojson'
geo = gpd.read_file(url)
name_to_uf = {
    'Acre':'AC','Alagoas':'AL','Amapá':'AP','Amazonas':'AM','Bahia':'BA','Ceará':'CE','Distrito Federal':'DF',
    'Espírito Santo':'ES','Goiás':'GO','Maranhão':'MA','Mato Grosso':'MT','Mato Grosso do Sul':'MS','Minas Gerais':'MG',
    'Pará':'PA','Paraíba':'PB','Paraná':'PR','Pernambuco':'PE','Piauí':'PI','Rio de Janeiro':'RJ','Rio Grande do Norte':'RN',
    'Rio Grande do Sul':'RS','Rondônia':'RO','Roraima':'RR','Santa Catarina':'SC','São Paulo':'SP','Sergipe':'SE','Tocantins':'TO'}
geo['UF'] = geo['name'].map(name_to_uf)

merged = geo.merge(summary, on='UF', how='left')

# Plot
fig, ax = plt.subplots(1, 1, figsize=(8, 9))
merged.plot(column='cluster', ax=ax, cmap='tab10', linewidth=0.5, edgecolor='white', legend=True)
ax.set_title('Clusters territoriais por UF (dominante)')
ax.axis('off')

# Annotate counts
for _, row in merged.dropna(subset=['UF']).iterrows():
    if row['geometry'] is None:
        continue
    x, y = row['geometry'].representative_point().coords[0]
    cnt = int(row['count']) if not pd.isna(row['count']) else 0
    ax.text(x, y, f"{row['UF']}\n(n={cnt})", fontsize=7, ha='center', va='center')

out = PLOTS/'territorial_clustering_map.png'
plt.tight_layout()
plt.savefig(out, dpi=200, bbox_inches='tight')
print('Saved PNG map to', out)

