# Relatório de Limpeza e Auditoria de Notebooks

- Arquivos arquivados: 19
- Notebooks auditados: 5

## Notebooks auditados
- notebooks\chilli_beans_analysis.ipynb
- notebooks\model_comparison_colab.ipynb
- notebooks\preprocessamento\eda_distribuicoes.ipynb
- notebooks\preprocessamento\eda_geografico.ipynb
- notebooks\preprocessamento\eda_indice.ipynb

## Problemas encontrados (amostra)
- [needs-interpretation] notebooks\chilli_beans_analysis.ipynb (célula 12): Adicionar célula markdown após o gráfico com interpretação dos resultados.
- [missing-xlabel] notebooks\chilli_beans_analysis.ipynb (célula 13): Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel).
- [missing-ylabel] notebooks\chilli_beans_analysis.ipynb (célula 13): Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel).
- [missing-legend] notebooks\chilli_beans_analysis.ipynb (célula 13): Incluir legenda clara quando houver múltiplas séries (plt.legend ou ax.legend).
- [needs-interpretation] notebooks\chilli_beans_analysis.ipynb (célula 15): Adicionar célula markdown após o gráfico com interpretação dos resultados.
- [low-comment-density] notebooks\chilli_beans_analysis.ipynb (célula 16): Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas.
- [missing-xlabel] notebooks\chilli_beans_analysis.ipynb (célula 16): Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel).
- [missing-ylabel] notebooks\chilli_beans_analysis.ipynb (célula 16): Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel).
- [missing-xlabel] notebooks\chilli_beans_analysis.ipynb (célula 19): Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel).
- [missing-ylabel] notebooks\chilli_beans_analysis.ipynb (célula 19): Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel).
- [needs-interpretation] notebooks\chilli_beans_analysis.ipynb (célula 19): Adicionar célula markdown após o gráfico com interpretação dos resultados.
- [low-comment-density] notebooks\chilli_beans_analysis.ipynb (célula 20): Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas.
- [needs-interpretation] notebooks\chilli_beans_analysis.ipynb (célula 25): Adicionar célula markdown após o gráfico com interpretação dos resultados.
- [missing-xlabel] notebooks\chilli_beans_analysis.ipynb (célula 26): Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel).
- [missing-ylabel] notebooks\chilli_beans_analysis.ipynb (célula 26): Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel).
- [low-comment-density] notebooks\chilli_beans_analysis.ipynb (célula 28): Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas.
- [missing-xlabel] notebooks\chilli_beans_analysis.ipynb (célula 28): Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel).
- [missing-ylabel] notebooks\chilli_beans_analysis.ipynb (célula 28): Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel).
- [missing-legend] notebooks\chilli_beans_analysis.ipynb (célula 28): Incluir legenda clara quando houver múltiplas séries (plt.legend ou ax.legend).
- [needs-interpretation] notebooks\chilli_beans_analysis.ipynb (célula 32): Adicionar célula markdown após o gráfico com interpretação dos resultados.
- [low-comment-density] notebooks\chilli_beans_analysis.ipynb (célula 33): Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas.
- [missing-title] notebooks\chilli_beans_analysis.ipynb (célula 33): Adicionar título descritivo ao gráfico (plt.title ou ax.set_title).
- [missing-xlabel] notebooks\chilli_beans_analysis.ipynb (célula 33): Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel).
- [missing-ylabel] notebooks\chilli_beans_analysis.ipynb (célula 33): Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel).
- [missing-legend] notebooks\chilli_beans_analysis.ipynb (célula 33): Incluir legenda clara quando houver múltiplas séries (plt.legend ou ax.legend).
- [missing-xlabel] notebooks\chilli_beans_analysis.ipynb (célula 37): Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel).
- [missing-ylabel] notebooks\chilli_beans_analysis.ipynb (célula 37): Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel).
- [missing-xlabel] notebooks\chilli_beans_analysis.ipynb (célula 40): Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel).
- [missing-ylabel] notebooks\chilli_beans_analysis.ipynb (célula 40): Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel).
- [low-comment-density] notebooks\chilli_beans_analysis.ipynb (célula 42): Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas.
- [missing-title] notebooks\chilli_beans_analysis.ipynb (célula 42): Adicionar título descritivo ao gráfico (plt.title ou ax.set_title).
- [missing-xlabel] notebooks\chilli_beans_analysis.ipynb (célula 42): Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel).
- [missing-ylabel] notebooks\chilli_beans_analysis.ipynb (célula 42): Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel).
- [low-comment-density] notebooks\chilli_beans_analysis.ipynb (célula 44): Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas.
- [missing-xlabel] notebooks\chilli_beans_analysis.ipynb (célula 44): Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel).
- [missing-ylabel] notebooks\chilli_beans_analysis.ipynb (célula 44): Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel).
- [low-comment-density] notebooks\chilli_beans_analysis.ipynb (célula 47): Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas.
- [low-comment-density] notebooks\chilli_beans_analysis.ipynb (célula 52): Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas.
- [low-comment-density] notebooks\chilli_beans_analysis.ipynb (célula 56): Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas.
- [missing-xlabel] notebooks\chilli_beans_analysis.ipynb (célula 60): Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel).
- [missing-ylabel] notebooks\chilli_beans_analysis.ipynb (célula 60): Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel).
- [missing-context-before-code] notebooks\preprocessamento\eda_distribuicoes.ipynb (célula 0): Inserir célula markdown antes desta célula explicando objetivo e o que será executado.
- [low-comment-density] notebooks\preprocessamento\eda_distribuicoes.ipynb (célula 11): Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas.
- [missing-xlabel] notebooks\preprocessamento\eda_distribuicoes.ipynb (célula 11): Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel).
- [missing-ylabel] notebooks\preprocessamento\eda_distribuicoes.ipynb (célula 11): Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel).
- [needs-interpretation] notebooks\preprocessamento\eda_distribuicoes.ipynb (célula 11): Adicionar célula markdown após o gráfico com interpretação dos resultados.
- [missing-xlabel] notebooks\preprocessamento\eda_distribuicoes.ipynb (célula 12): Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel).
- [missing-ylabel] notebooks\preprocessamento\eda_distribuicoes.ipynb (célula 12): Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel).
- [missing-legend] notebooks\preprocessamento\eda_distribuicoes.ipynb (célula 12): Incluir legenda clara quando houver múltiplas séries (plt.legend ou ax.legend).
- [missing-xlabel] notebooks\preprocessamento\eda_distribuicoes.ipynb (célula 14): Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel).

## Recomendações prioritárias
1. Garantir títulos, labels de eixos e legendas em todos os gráficos (consistência visual).
2. Inserir título e contexto no topo de cada notebook (objetivo e escopo).
3. Adicionar comentários explicativos em células de código extensas (>10 linhas).
4. Incluir interpretação (markdown) logo após gráficos e tabelas relevantes.