from pathlib import Path
import sys
import pandas as pd

BASE = Path(__file__).resolve().parents[1]
sys.path.append(str(BASE))
from src.geo_utils import normalize_city_series

# Prefer enriched-by-UF if available; else use time-safe cleaned dataset
CANDIDATES = [
    BASE/'data'/'processed'/'features_enriched_uf.csv',
    BASE/'data'/'processed'/'features_cleaned_pre_territorial_time.csv',
    BASE/'data'/'processed'/'features_cleaned_pre_territorial.csv',
]
INPUT = None
for c in CANDIDATES:
    if c.exists():
        INPUT = c; break

MUNI_INDICATORS = BASE/'data'/'external'/'municipal_indicators.csv'
OUT = BASE/'data'/'processed'/'features_enriched_municipio.csv'

TEMPLATE_COLUMNS = [
    'UF', 'Cidade_std',
    'PIB_per_capita_mun',
    'Populacao_mun',
    'IDH_mun',
    'Renda_media',
    'Taxa_desemprego_mun',
]


def ensure_template():
    MUNI_INDICATORS.parent.mkdir(parents=True, exist_ok=True)
    if not MUNI_INDICATORS.exists():
        pd.DataFrame(columns=TEMPLATE_COLUMNS).to_csv(MUNI_INDICATORS, index=False)
        print('Template criado para indicadores municipais em:', MUNI_INDICATORS)
        print('Preencha este arquivo com dados (IBGE/Atlas/PNAD) e reexecute o script.')
        return False
    return True


def main():
    if INPUT is None:
        raise SystemExit('Nenhum dataset base encontrado para enriquecimento municipal.')

    has_template = ensure_template()
    if not has_template:
        return

    df = pd.read_csv(INPUT)

    # Ensure UF and Cidade_std
    if 'UF' not in df.columns:
        raise SystemExit('Coluna UF ausente. Execute primeiro o enriquecimento por UF.')
    if 'Cidade_std' not in df.columns:
        if 'Cidade' in df.columns:
            df['Cidade_std'] = normalize_city_series(df['Cidade'])
        elif 'cidade' in df.columns:
            df['Cidade_std'] = normalize_city_series(df['cidade'])
        else:
            df['Cidade_std'] = ''

    ind = pd.read_csv(MUNI_INDICATORS)
    if not {'UF','Cidade_std'}.issubset(ind.columns):
        raise SystemExit('O arquivo municipal_indicators.csv deve conter colunas UF e Cidade_std.')
    ind['UF'] = ind['UF'].astype(str).str.strip().str.upper()
    ind['Cidade_std'] = normalize_city_series(ind['Cidade_std']) if 'Cidade_std' in ind.columns else ind['Cidade_std']

    merged = df.merge(ind, on=['UF','Cidade_std'], how='left')
    OUT.parent.mkdir(parents=True, exist_ok=True)
    merged.to_csv(OUT, index=False)
    print('Enriquecimento municipal concluído:', OUT)


if __name__ == '__main__':
    main()

