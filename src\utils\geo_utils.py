"""Utilidades para carregamento e junção de shapes geográficos do Brasil (UF) e criação de choropleths.

Prioriza geobr; fallback para geoBoundaries se geobr não estiver instalado.
"""
from __future__ import annotations
from typing import Optional, Dict, Any

# Dicionário que mapeia as siglas dos estados para seus códigos IBGE
# Os códigos seguem um padrão regional:
# 1x = Norte
# 2x = Nordeste 
# 3x = Sudeste
# 4x = Sul
# 5x = Centro-Oeste
UF_TO_CODE_IBGE = {
    # Região Norte
    'RO': 11, 'AC': 12, 'AM': 13, 'RR': 14, 'PA': 15, 'AP': 16, 'TO': 17,
    # Regi<PERSON> Nordeste
    'MA': 21, 'PI': 22, 'CE': 23, 'RN': 24, 'PB': 25, 'PE': 26, 'AL': 27, 'SE': 28, 'BA': 29,
    # Região Sudeste
    'MG': 31, 'ES': 32, 'RJ': 33, 'SP': 35,
    # Região Sul
    'PR': 41, 'SC': 42, 'RS': 43,
    # Região Centro-Oeste
    'MS': 50, 'MT': 51, 'GO': 52, 'DF': 53
}

GEOBOUNDARIES_UF_URL = "https://raw.githubusercontent.com/wmgeolab/geoBoundaries/main/releaseData/gbOpen/BRA/ADM1/geoBoundaries-BRA-ADM1.geojson"


def load_states_geojson(simplify: bool = True) -> Dict[str, Any]:
    """Tenta carregar GeoJSON de estados.
    1. geobr (se disponível) -> to_crs EPSG:4326 -> __geo_interface__
    2. requests.get geoBoundaries
    """
    # Tentativa geobr
    try:
        import geobr
        gdf = geobr.read_state()
        # garante WGS84
        if gdf.crs is not None and gdf.crs.to_epsg() != 4326:
            gdf = gdf.to_crs(4326)
        # opcional: simplificação (geopandas >=0.10)
        # Verifica se deve simplificar e se o método está disponível
        if simplify and hasattr(gdf, 'simplify'):
            try:
                # Simplifica as geometrias para reduzir complexidade
                # 0.01 é o fator de tolerância para simplificação
                # preserve_topology=True mantém a integridade das fronteiras
                gdf['geometry'] = gdf.geometry.simplify(0.01, preserve_topology=True)
            except Exception:
                # Ignora erros na simplificação - usa geometria original
                pass
        
        # Converte o GeoDataFrame para formato GeoJSON
        return gdf.__geo_interface__
    except Exception:
        # Se falhar com geobr, continua para tentar geoBoundaries
        pass
    # Fallback geoBoundaries
    try:
        import requests
        r = requests.get(GEOBOUNDARIES_UF_URL, timeout=30)
        r.raise_for_status()
        return r.json()
    except Exception as e:
        raise RuntimeError(f"Falha ao obter GeoJSON de UF: {e}")

# Tenta adivinhar campo de código da UF dentro das properties
def infer_featureidkey(geojson: Dict[str, Any]) -> str:
    

    # Olha primeira feature
    feat = geojson['features'][0]
    props = feat.get('properties', {})
    candidates = ['code_state', 'CD_GEOCUF', 'STATEFP', 'shapeID', 'sigla', 'abbrev_state']
    for c in candidates:
        if c in props:
            return f"properties.{c}"
    # fallback genérico (primeira chave)
    if props:
        return f"properties.{list(props.keys())[0]}"
    raise ValueError('Não foi possível inferir featureidkey.')

__all__ = ['UF_TO_CODE_IBGE', 'load_states_geojson', 'infer_featureidkey']
