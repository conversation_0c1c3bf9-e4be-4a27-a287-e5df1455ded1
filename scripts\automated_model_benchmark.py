import warnings
warnings.filterwarnings("ignore")
from pathlib import Path
from typing import List, Dict, <PERSON>ple
import numpy as np
import pandas as pd

from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_absolute_error, mean_squared_error

# Optional models
try:
    import lightgbm as lgb
except Exception:
    lgb = None

try:
    from xgboost import XGBRegressor
except Exception:
    XGBRegressor = None

from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, IsolationForest
from sklearn.svm import SVR
from sklearn.linear_model import Ridge, Lasso
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
from sklearn.neighbors import NearestNeighbors
from sklearn.neural_network import MLPRegressor
from sklearn.base import BaseEstimator, TransformerMixin

# ----- Metrics -----

def wape(y_true: np.ndarray, y_pred: np.ndarray) -> float:
    """Weighted Absolute Percentage Error (robusto com eps)."""
    eps = 1e-9
    den = np.abs(y_true).sum() + eps
    return float(np.abs(y_true - y_pred).sum() / den)

def smape(y_true: np.ndarray, y_pred: np.ndarray) -> float:
    """Symmetric Mean Absolute Percentage Error (robusto com eps)."""
    eps = 1e-9
    num = 2 * np.abs(y_true - y_pred)
    den = (np.abs(y_true) + np.abs(y_pred)) + eps
    return float((num / den).mean())

# ----- Encoding utils (target encoding seguro) -----

def _target_encode(train_cat: pd.Series, train_y: pd.Series, apply_cat: pd.Series,
                   min_samples: int = 5, smoothing: float = 10.0) -> np.ndarray:
    """
    Target encoding suave, calculado apenas em dados de treino e aplicado em validação/teste.
    - min_samples: mínimo para reduzir overfitting em categorias raras
    - smoothing: controla o peso entre média global e média por categoria
    """
    df_tmp = pd.DataFrame({"cat": train_cat.values, "y": train_y.values})
    global_mean = df_tmp["y"].mean()
    stats = df_tmp.groupby("cat").agg(n=("y", "count"), mean=("y", "mean"))
    # smoothing weight
    stats["weight"] = stats["n"] / (stats["n"] + smoothing)
    stats["te"] = stats["weight"] * stats["mean"] + (1 - stats["weight"]) * global_mean
    te_map = stats["te"].to_dict()
    enc = apply_cat.map(te_map).fillna(global_mean).values
    return enc

# ----- Unsupervised/Feature Augmenters -----
class KMeansLabeler(TransformerMixin, BaseEstimator):
    def __init__(self, n_clusters: int = 3, random_state: int = 42):
        self.n_clusters = n_clusters
        self.random_state = random_state
        self.km = None
    def fit(self, X, y=None):
        from sklearn.cluster import KMeans
        self.km = KMeans(n_clusters=self.n_clusters, random_state=self.random_state, n_init="auto")
        self.km.fit(X)
        return self
    def transform(self, X):
        labels = self.km.predict(X).reshape(-1, 1)
        return np.hstack([X, labels])

class DBSCANApproxLabeler(TransformerMixin, BaseEstimator):
    def __init__(self, eps: float = 0.5, min_samples: int = 5):
        self.eps = eps
        self.min_samples = min_samples
        self.db = None
        self.nn = None
        self.train_X = None
        self.labels_ = None
    def fit(self, X, y=None):
        from sklearn.cluster import DBSCAN
        self.db = DBSCAN(eps=self.eps, min_samples=self.min_samples)
        self.db.fit(X)
        self.train_X = X
        self.labels_ = self.db.labels_
        # NN fallback to assign labels for new points
        self.nn = NearestNeighbors(n_neighbors=1)
        self.nn.fit(self.train_X)
        return self
    def transform(self, X):
        # Assign label of nearest neighbor from training set
        dist, idx = self.nn.kneighbors(X)
        nn_idx = idx.reshape(-1)
        labels = self.labels_[nn_idx].reshape(-1, 1)
        return np.hstack([X, labels])

class IsolationScore(TransformerMixin, BaseEstimator):
    def __init__(self, n_estimators: int = 100, random_state: int = 42):
        self.n_estimators = n_estimators
        self.random_state = random_state
        self.iso = None
    def fit(self, X, y=None):
        self.iso = IsolationForest(n_estimators=self.n_estimators, random_state=self.random_state)
        self.iso.fit(X)
        return self
    def transform(self, X):
        # Higher values = more anomalous (invert sign of score to be positive for anomalies)
        scores = -self.iso.score_samples(X).reshape(-1, 1)
        return np.hstack([X, scores])


# ----- Feature Engineering (no leakage) -----

def _add_time_features(df: pd.DataFrame) -> pd.DataFrame:
    df = df.copy()
    df = df.sort_values(["id_loja", "data"])  # ensure ordering
    df["dow"] = df["data"].dt.dayofweek
    df["month"] = df["data"].dt.month
    df["year"] = df["data"].dt.year
    df["is_weekend"] = (df["dow"] >= 5).astype(int)
    first_dates = df.groupby("id_loja")["data"].transform("min")
    df["store_age_days"] = (df["data"] - first_dates).dt.days
    # Lags
    for lag in [1, 2, 3, 7, 14]:
        df[f"valor_lag_{lag}"] = df.groupby("id_loja")["valor"].shift(lag)
    # Rolling defasado
    df["valor_ma_7"] = df.groupby("id_loja")["valor"].shift(1).rolling(7, min_periods=3).mean()
    df["valor_ma_14"] = df.groupby("id_loja")["valor"].shift(1).rolling(14, min_periods=5).mean()
    # Qty lags
    if "qtd" in df.columns:
        df["qtd_lag_1"] = df.groupby("id_loja")["qtd"].shift(1)
        df["qtd_lag_7"] = df.groupby("id_loja")["qtd"].shift(7)
    # Cyclical encoding
    df["dow_sin"] = np.sin(2 * np.pi * df["dow"] / 7)
    df["dow_cos"] = np.cos(2 * np.pi * df["dow"] / 7)
    df["month_sin"] = np.sin(2 * np.pi * (df["month"] - 1) / 12)
    df["month_cos"] = np.cos(2 * np.pi * (df["month"] - 1) / 12)


    return df


def _add_ticket_and_demographics(df: pd.DataFrame) -> pd.DataFrame:
    df = df.copy()
    # Ticket médio por observação: evitar divisão por zero
    if "qtd" in df.columns and "valor" in df.columns:
        df["ticket"] = df["valor"] / df["qtd"].replace(0, np.nan)
    # Ticket médio por loja (estático ao longo do tempo) calculado sobre histórico passado
    if "id_loja" in df.columns:
        ticket_medio_loja = df.groupby("id_loja")["ticket"].transform("mean") if "ticket" in df.columns else np.nan
        df["ticket_medio_loja"] = ticket_medio_loja
        # versão defasada 7 dias por loja (sem vazamento)
        if "ticket" in df.columns:
            df["ticket_medio_loja_lag_7"] = (
                df.groupby("id_loja")["ticket"].shift(1).rolling(7, min_periods=3).mean()
            )
    # Ticket relativo vs média do UF (sem vazamento: usa shift+rolling por UF ao longo do tempo)
    if "ticket" in df.columns and "uf" in df.columns:
        df = df.sort_values(["uf","data"])  # garantir ordem temporal
        mean_ticket_uf = df.groupby(["uf"]) ["ticket"].shift(1).rolling(7, min_periods=3).mean()
        df["ticket_relativo_vs_media_uf"] = df["ticket"] / mean_ticket_uf
    # Densidade de lojas por UF (proxy)
    if "uf" in df.columns and "id_loja" in df.columns:
        lojas_por_uf = df.groupby("uf")["id_loja"].transform("nunique")
        df["densidade_lojas_uf"] = lojas_por_uf
    # Receita per capita estimada (proxy): receita acumulada da loja / (lojas_por_uf * fator)
    # Como não temos população, usamos proxy simples pela própria densidade de lojas
    if "valor" in df.columns and "densidade_lojas_uf" in df.columns:
        receita_loja = df.groupby("id_loja")["valor"].transform("sum")
        df["receita_per_capita_estimada"] = receita_loja / (df["densidade_lojas_uf"].replace(0, np.nan))
    # Ticket normalizado pela densidade
    if "ticket_medio_loja" in df.columns and "densidade_lojas_uf" in df.columns:
        df["ticket_normalizado_densidade"] = df["ticket_medio_loja"] / (1.0 + df["densidade_lojas_uf"])
    return df
def _add_tmmn(df: pd.DataFrame) -> pd.DataFrame:
    """Calcula TMMN (Ticket Médio Marginal Normalizado):
    tmmn = (ticket_medio_loja / media_ticket_macro) / ((1 + densidade_norm) * (1 + 1/receita_pc_norm))
    onde densidade_norm e receita_pc_norm são normalizadas por min-max robusto (quantis 5-95%).
    """
    df = df.copy()
    # média de ticket por macroregiao (sem vazamento: usar rolling por macro + shift)
    if "macroregiao" in df.columns and ("ticket_medio_loja_lag_7" in df.columns or "ticket_medio_loja" in df.columns):
        # média móvel de ticket_medio por macro (defasado; usa lag_7 quando disponível)
        ticket_ref_col = "ticket_medio_loja_lag_7" if "ticket_medio_loja_lag_7" in df.columns else "ticket_medio_loja"
        df = df.sort_values(["macroregiao","data"]) if "data" in df.columns else df
        macro_mean_ticket = df.groupby("macroregiao")[ticket_ref_col].shift(1).rolling(7, min_periods=1).mean()
        df["media_ticket_macro"] = macro_mean_ticket
    # normalizações robustas
    def _robust_min_max(s: pd.Series) -> pd.Series:
        s = s.astype(float)
        q05 = np.nanpercentile(s, 5) if np.isfinite(s).any() else 0.0
        q95 = np.nanpercentile(s, 95) if np.isfinite(s).any() else 1.0
        if q95 - q05 == 0:
            return (s - q05)
        return (s - q05) / (q95 - q05)
    if "densidade_lojas_uf" in df.columns:
        df["densidade_norm"] = _robust_min_max(df["densidade_lojas_uf"].replace([np.inf, -np.inf], np.nan))
    if "receita_per_capita_estimada" in df.columns:
        df["receita_pc_norm"] = _robust_min_max(df["receita_per_capita_estimada"].replace([np.inf, -np.inf], np.nan))
    # cálculo do TMMN
    eps = 1e-9
    if {"ticket_medio_loja","media_ticket_macro","densidade_norm","receita_pc_norm"}.issubset(df.columns):
        t_base = (df["ticket_medio_loja"] / (df["media_ticket_macro"].replace(0, np.nan)))
        denom = (1.0 + df["densidade_norm"]) * (1.0 + 1.0 / (df["receita_pc_norm"] + eps))
        df["tmmn"] = (t_base / denom).replace([np.inf, -np.inf], np.nan)
    return df


    return df


def _default_feature_cols(df: pd.DataFrame) -> List[str]:
    cols = [
        "dow", "month", "year", "is_weekend", "store_age_days",
        "valor_lag_1", "valor_lag_2", "valor_lag_3", "valor_lag_7", "valor_lag_14",
        "valor_ma_7", "valor_ma_14",
        "dow_sin", "dow_cos", "month_sin", "month_cos",
        # tickets e proxies
        "ticket_medio_loja", "ticket_medio_loja_lag_7", "ticket_relativo_vs_media_uf",
        "densidade_lojas_uf", "receita_per_capita_estimada", "ticket_normalizado_densidade",
    ]
    if "qtd_lag_1" in df.columns:
        cols.append("qtd_lag_1")
    if "qtd_lag_7" in df.columns:
        cols.append("qtd_lag_7")
    return [c for c in cols if c in df.columns]

# ----- Regional helpers -----

_MACRO_REGIAO_MAP = {
    # Norte
    "AC":"Norte","AM":"Norte","AP":"Norte","PA":"Norte","RO":"Norte","RR":"Norte","TO":"Norte",
    # Nordeste
    "AL":"Nordeste","BA":"Nordeste","CE":"Nordeste","MA":"Nordeste","PB":"Nordeste","PE":"Nordeste","PI":"Nordeste","RN":"Nordeste","SE":"Nordeste",
    # Centro-Oeste
    "DF":"Centro-Oeste","GO":"Centro-Oeste","MS":"Centro-Oeste","MT":"Centro-Oeste",
    # Sudeste
    "ES":"Sudeste","MG":"Sudeste","RJ":"Sudeste","SP":"Sudeste",
    # Sul
    "PR":"Sul","RS":"Sul","SC":"Sul",
}

def _add_macroregiao(df: pd.DataFrame) -> pd.DataFrame:
    if "uf" in df.columns and "macroregiao" not in df.columns:
        df["macroregiao"] = df["uf"].astype(str).str.upper().map(_MACRO_REGIAO_MAP)
    return df


# ----- Baselines -----

def _baseline_predictions(train: pd.Series, horizon: int) -> Dict[str, np.ndarray]:
    preds = {}
    # Naive last value
    last_val = train.iloc[-1] if len(train) else train.mean()
    preds["naive"] = np.full(horizon, last_val)
    # Rolling mean 7 (train-only)
    rm7 = train.rolling(7, min_periods=3).mean().iloc[-1]
    rm7 = train.mean() if np.isnan(rm7) else rm7
    preds["mean7"] = np.full(horizon, rm7)
    # Seasonal (weekly) naive if possível
    if len(train) >= 7:
        preds["seasonal_naive"] = train.iloc[-7:].values
        if horizon > 7:
            reps = int(np.ceil(horizon / 7))
            preds["seasonal_naive"] = np.tile(preds["seasonal_naive"], reps)[:horizon]
    return preds


# ----- Model grids -----

def _model_grid():
    grids = []
    # LightGBM (opcional)
    if lgb is not None:
        for n in [50, 100]:
            for lr in [0.05, 0.1]:
                for md in [3, 5]:
                    grids.append((f"lgb_{n}_{lr}_{md}", lgb.LGBMRegressor(n_estimators=n, learning_rate=lr, max_depth=md, random_state=42, verbose=-1)))
    # Unsupervised augmenters (cluster labels / anomaly score)
    # These are added as separate pipelines to compare their effect without leakage
    for k in [3, 5]:
        grids.append((f"kmeanslbl_{k}_ridge", Pipeline([
            ("kml", KMeansLabeler(n_clusters=k, random_state=42)),
            ("sc", StandardScaler()),
            ("ridge", Ridge(alpha=1.0, random_state=42))
        ])))
    for eps in [0.5, 1.0]:
        grids.append((f"dbscanlbl_{eps}_ridge", Pipeline([
            ("db", DBSCANApproxLabeler(eps=eps, min_samples=5)),
            ("sc", StandardScaler()),
            ("ridge", Ridge(alpha=1.0, random_state=42))
        ])))
    # IsolationForest score as feature
    grids.append(("isoforest_ridge", Pipeline([
        ("iso", IsolationScore(n_estimators=100, random_state=42)),
        ("sc", StandardScaler()),
        ("ridge", Ridge(alpha=1.0, random_state=42))
    ])))

    # RandomForest
    for n in [100, 200]:
        for md in [None, 5, 10]:
            for leaf in [1, 3, 5]:
                grids.append((f"rf_{n}_{md}_{leaf}", RandomForestRegressor(n_estimators=n, max_depth=md, min_samples_leaf=leaf, random_state=42)))
    # XGBoost (opcional)
    if XGBoostRegressor := XGBRegressor:
        for n in [100]:
            for lr in [0.05, 0.1]:
                for md in [3, 5]:
                    grids.append((f"xgb_{n}_{lr}_{md}", XGBoostRegressor(n_estimators=n, learning_rate=lr, max_depth=md, random_state=42, objective="reg:squarederror")))
    # Gradient Boosting Regressor
    for n in [100, 200]:
        for lr in [0.05, 0.1]:
            for md in [2, 3]:
                grids.append((f"gbr_{n}_{lr}_{md}", GradientBoostingRegressor(n_estimators=n, learning_rate=lr, max_depth=md, random_state=42)))
    # SVR (RBF e poly)
    for C in [1.0, 10.0]:
        for eps in [0.1, 0.2]:
            grids.append((f"svr_rbf_C{C}_e{eps}", Pipeline([
                ("sc", StandardScaler()),
                ("svr", SVR(kernel="rbf", C=C, epsilon=eps))
            ])))
            grids.append((f"svr_poly_C{C}_e{eps}", Pipeline([
                ("sc", StandardScaler()),
                ("svr", SVR(kernel="poly", degree=3, C=C, epsilon=eps))
            ])))
    # Ridge/Lasso
    for alpha in [0.1, 1.0, 10.0]:
        grids.append((f"ridge_{alpha}", Pipeline([
            ("sc", StandardScaler()),
            ("ridge", Ridge(alpha=alpha, random_state=42))
        ])))
        grids.append((f"lasso_{alpha}", Pipeline([
            ("sc", StandardScaler()),
            ("lasso", Lasso(alpha=alpha, random_state=42, max_iter=10000))
        ])))
    # MLP (opcional, grades pequenas)
    for hl in [(64,), (64,32)]:
        grids.append((f"mlp_{'_'.join(map(str,hl))}", Pipeline([
            ("sc", StandardScaler()),
            ("mlp", MLPRegressor(hidden_layer_sizes=hl, activation="relu", learning_rate_init=0.001, max_iter=200, random_state=42))
        ])))
    # PCA + Regression (Ridge)
    for ncomp in [5, 10, 20]:
        grids.append((f"pca_ridge_{ncomp}", Pipeline([
            ("sc", StandardScaler()),
            ("pca", PCA(n_components=ncomp, random_state=42)),
            ("ridge", Ridge(alpha=1.0, random_state=42))
        ])))
    return grids
    # Ridge/Lasso
    for alpha in [0.1, 1.0, 10.0]:
        grids.append((f"ridge_{alpha}", Pipeline([
            ("sc", StandardScaler()),
            ("ridge", Ridge(alpha=alpha, random_state=42))
        ])))
        grids.append((f"lasso_{alpha}", Pipeline([
            ("sc", StandardScaler()),
            ("lasso", Lasso(alpha=alpha, random_state=42, max_iter=10000))
        ])))
    # MLP (opcional, grades pequenas)
    for hl in [(64,), (64,32)]:
        grids.append((f"mlp_{'_'.join(map(str,hl))}", Pipeline([
            ("sc", StandardScaler()),
            ("mlp", MLPRegressor(hidden_layer_sizes=hl, activation="relu", learning_rate_init=0.001, max_iter=200, random_state=42))
        ])))
    # PCA + Regression (Ridge)
    for ncomp in [5, 10, 20]:
        grids.append((f"pca_ridge_{ncomp}", Pipeline([
            ("sc", StandardScaler()),
            ("pca", PCA(n_components=ncomp, random_state=42)),
            ("ridge", Ridge(alpha=1.0, random_state=42))
        ])))
    return grids


# ----- Benchmark runner -----

def run_model_comparison(df_clean: pd.DataFrame, output_root: Path) -> Dict[str, any]:
    """Run automated benchmark with temporal CV and holdout.
    Expects df_clean with columns: data, id_loja, valor, optional: qtd, uf, cidade, Tipo_PDV.
    """
    out_tables = output_root / "tables"
    out_plots = output_root / "plots" / "model_comparison"
    out_tables.mkdir(parents=True, exist_ok=True)
    out_plots.mkdir(parents=True, exist_ok=True)
    # Experimental interactive plots subfolder (Plotly/Altair/Bokeh)
    out_plots_exp = out_plots / "experimental"
    out_plots_exp.mkdir(parents=True, exist_ok=True)

    # Feature engineering (no leakage)
    df = _add_time_features(df_clean)
    df = _add_macroregiao(df)
    df = _add_ticket_and_demographics(df)
    df = _add_tmmn(df)

    # Preservar colunas originais para segmentações
    for c in ["uf", "Tipo_PDV", "macroregiao"]:
        if c in df.columns:
            df[f"{c}_label"] = df[c]
    # One-hot para baixa cardinalidade (sem vazamento)
    cat_low = [c for c in ["uf", "Tipo_PDV", "macroregiao"] if c in df.columns]
    if cat_low:
        df = pd.get_dummies(df, columns=cat_low, drop_first=False)

    # Keep only rows where lags are available
    feat_cols = _default_feature_cols(df)
    # add dummy columns into features
    feat_cols += [c for c in df.columns if c.startswith("uf_") or c.startswith("Tipo_PDV_") or c.startswith("macroregiao_")]
    feat_cols = list(dict.fromkeys([c for c in feat_cols if c in df.columns]))

    keep_cols = feat_cols + ["valor", "data", "id_loja", "uf_label", "cidade", "Tipo_PDV_label", "macroregiao_label", "tmmn"]
    keep_cols = [c for c in keep_cols if c in df.columns]
    dfm = df[keep_cols].dropna().copy()

    # Save engineered dataset
    processed_dir = output_root.parents[1] / "data" / "processed"
    try:
        processed_dir.mkdir(parents=True, exist_ok=True)
        dfm.to_csv(processed_dir / "features_engineered.csv", index=False)
    except Exception:
        pass

    # Prepare holdout final: adaptive per-store tail (up to 14 days, at least 1 day or 20% of store history)
    dfm = dfm.sort_values(["id_loja", "data"]).copy()
    holdout_mask = pd.Series(False, index=dfm.index)
    for loja, g in dfm.groupby("id_loja", sort=False):
        dates = g["data"].drop_duplicates().sort_values()
        n_days = len(dates)
        k = min(14, max(1, int(np.ceil(n_days * 0.2))))
        cutoff_date = dates.iloc[-k]
        holdout_mask.loc[g.index] = g["data"] >= cutoff_date

    df_train = dfm[~holdout_mask].copy()
    df_holdout = dfm[holdout_mask].copy()

    # Guard: if no train, relax holdout to last 1 day per store
    if len(df_train) == 0:
        holdout_mask = pd.Series(False, index=dfm.index)
        for loja, g in dfm.groupby("id_loja", sort=False):
            last_date = g["data"].max()
            holdout_mask.loc[g.index] = g["data"] == last_date
        df_train = dfm[~holdout_mask].copy()
        df_holdout = dfm[holdout_mask].copy()

    X = df_train[feat_cols].values if len(df_train) else np.zeros((0, len(feat_cols)))
    y = df_train["valor"].values.astype(float) if len(df_train) else np.array([])

    results = []

    # TimeSeriesSplit CV (adaptive)
    n_samples = len(X)
    if n_samples >= 30:
        n_splits = 3
    elif n_samples >= 10:
        n_splits = 2
    else:
        n_splits = 2
    tscv = TimeSeriesSplit(n_splits=n_splits)

    # Baselines on holdout per store (using the same per-store cutoff logic)
    base_rows = []
    # Optional: Temporal K-fold target encoding for high-cardinality categoricals
    # Applies within CV only; for holdout, fit on full train and apply to holdout
    high_card_cols = [c for c in ["id_loja", "cidade"] if c in dfm.columns]
    def make_te_features(df_ref: pd.DataFrame, y_ref: np.ndarray, df_apply: pd.DataFrame) -> Tuple[np.ndarray, List[str]]:
        te_feats = []
        te_names = []
        for col in high_card_cols:
            te_col = _target_encode(df_ref[col], pd.Series(y_ref, index=df_ref.index), df_apply[col])
            te_feats.append(te_col.reshape(-1, 1))
            te_names.append(f"{col}_te")
        if not te_feats:
            return np.empty((len(df_apply), 0)), []
        return np.hstack(te_feats), te_names

    # Precompute per-store cutoff date used above
    cutoff_map = dfm.groupby("id_loja")["data"].apply(lambda s: s.drop_duplicates().sort_values().iloc[-min(14, max(1, int(np.ceil(s.drop_duplicates().shape[0] * 0.2))))])
    for loja, g in dfm.sort_values("data").groupby("id_loja"):
        cutoff_date = cutoff_map.get(loja, g["data"].max())
        train_g = g[g["data"] < cutoff_date]
        test_g = g[g["data"] >= cutoff_date]
        if len(test_g) == 0 or len(train_g) == 0:
            continue
        preds = _baseline_predictions(train_g.set_index("data")["valor"], len(test_g))
        for bname, bp in preds.items():
            bp = bp[: len(test_g)]
            base_rows.append({
                "model": f"baseline_{bname}",
                "id_loja": loja,
                "WAPE": wape(test_g["valor"].values, bp),
                "sMAPE": smape(test_g["valor"].values, bp),
                "MAE": mean_absolute_error(test_g["valor"].values, bp),
            })
    if base_rows:
        base_df = pd.DataFrame(base_rows)
        base_df.to_csv(out_tables / "baseline_holdout_metrics.csv", index=False)
    cv_details: Dict[str, pd.DataFrame] = {}


    # Model grids
    grids = _model_grid()

    # CV loop
    for model_name, model in grids:
        fold_metrics = []
        for fold, (tr_idx, val_idx) in enumerate(tscv.split(X), 1):
            X_tr, X_val = X[tr_idx], X[val_idx]
            y_tr, y_val = y[tr_idx], y[val_idx]
            if len(np.unique(y_tr)) < 2:
                continue
            # Safe target encoding fitted on train fold only
            te_tr, te_names = make_te_features(df_train.iloc[tr_idx], y_tr, df_train.iloc[tr_idx])
            te_val, _ = make_te_features(df_train.iloc[tr_idx], y_tr, df_train.iloc[val_idx])
            X_tr_ext = np.hstack([X_tr, te_tr]) if te_tr.size else X_tr
            X_val_ext = np.hstack([X_val, te_val]) if te_val.size else X_val
            try:
                model.fit(X_tr_ext, y_tr)
                pred = model.predict(X_val_ext)
            except Exception:
                continue
            fold_metrics.append({
                "fold": fold,
                "WAPE": wape(y_val, pred),
                "sMAPE": smape(y_val, pred),
                "MAE": mean_absolute_error(y_val, pred),
                "RMSE": mean_squared_error(y_val, pred, squared=False),
            })


    # Comparative plots e uplift vs baseline
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns
        sns.set(style="whitegrid")
        # Load baseline metrics
        # TMMN por macro-região
        if "macroregiao_label" in dfm.columns and "tmmn" in dfm.columns:
            try:
                tmmn_grp = dfm.groupby("macroregiao_label")["tmmn"].mean().reset_index().rename(columns={"macroregiao_label":"macroregiao","tmmn":"TMMN_mean"})
                tmmn_grp.to_csv(out_tables / "tmmn_by_macroregiao.csv", index=False)
                plt.figure(figsize=(10,6))
                sns.barplot(data=tmmn_grp, x="macroregiao", y="TMMN_mean")
                plt.xticks(rotation=20)
                plt.title("TMMN médio por Macro-região")
                plt.tight_layout()
                plt.savefig(out_plots / "segmentation_tmmn_by_macroregiao.png", dpi=150, bbox_inches="tight")
                plt.close()
                # Experimental interactive versions (Plotly / Altair / Bokeh)
                # Plotly
                try:
                    import plotly.express as px
                    fig = px.bar(tmmn_grp, x="macroregiao", y="TMMN_mean", title="TMMN médio por Macro-região")
                    fig.write_html(str(out_plots_exp / "segmentation_tmmn_by_macroregiao_plotly.html"))
                except Exception:
                    pass
                # Altair
                try:
                    import altair as alt
                    alt.data_transformers.disable_max_rows()
                    chart = (
                        alt.Chart(tmmn_grp)
                        .mark_bar()
                        .encode(
                            x=alt.X("macroregiao:N", title="Macro-região"),
                            y=alt.Y("TMMN_mean:Q", title="TMMN médio"),
                            tooltip=["macroregiao:N", alt.Tooltip("TMMN_mean:Q", format=",.4f")]
                        )
                        .properties(title="TMMN médio por Macro-região")
                        .interactive()
                    )
                    chart.save(str(out_plots_exp / "segmentation_tmmn_by_macroregiao_altair.html"))
                except Exception:
                    pass
                # Bokeh
                try:
                    from bokeh.plotting import figure
                    from bokeh.models import ColumnDataSource, HoverTool
                    from bokeh.io import save
                    from bokeh.resources import CDN
                    src = ColumnDataSource(tmmn_grp)
                    p = figure(x_range=list(tmmn_grp["macroregiao"].astype(str).unique()),
                               title="TMMN médio por Macro-região",
                               sizing_mode="stretch_width", height=400)
                    p.vbar(x="macroregiao", top="TMMN_mean", width=0.6, source=src)
                    p.add_tools(HoverTool(tooltips=[("Macro-região","@macroregiao"), ("TMMN","@TMMN_mean{0.0000}")]))
                    save(p, filename=str(out_plots_exp / "segmentation_tmmn_by_macroregiao_bokeh.html"), resources=CDN)
                except Exception:
                    pass
            except Exception:
                pass

        baseline_path = out_tables / "baseline_holdout_metrics.csv"
        base_df = pd.read_csv(baseline_path) if baseline_path.exists() else pd.DataFrame()
        # Save comparative bars (CV vs HOLDOUT)
        # Macro-região segmentation
        if "macroregiao_label" in dfm.columns and len(df_holdout) > 0 and results:
            try:
                res_df = pd.DataFrame(results)
                best_model_name = res_df.iloc[0]["model"] if not res_df.empty else None
                if best_model_name:
                    te_train, _ = make_te_features(df_train, df_train["valor"].values, df_train)
                    X_train_full = np.hstack([df_train[feat_cols].values, te_train]) if te_train.size else df_train[feat_cols].values
                    best_est = None
                    for name, est in grids:
                        if name == best_model_name:
                            best_est = est
                            break
                    if best_est is not None:
                        best_est.fit(X_train_full, df_train["valor"].values)
                        te_hold, _ = make_te_features(df_train, df_train["valor"].values, df_holdout)
                        X_hold_full = np.hstack([df_holdout[feat_cols].values, te_hold]) if te_hold.size else df_holdout[feat_cols].values
                        pred_hold = best_est.predict(X_hold_full)
                        df_eval = df_holdout.copy()
                        df_eval["pred"] = pred_hold
                        df_eval["WAPE"] = np.abs(df_eval["valor"] - df_eval["pred"]) / (np.abs(df_eval["valor"]) + 1e-9)
                        grp = df_eval.groupby("macroregiao_label").agg(WAPE_mean=("WAPE","mean"),
                                                                 MAE=(lambda s: np.mean(np.abs(df_eval.loc[s.index, "valor"]-df_eval.loc[s.index, "pred"])), "mean")).reset_index()
                        grp.to_csv(out_tables / "metrics_by_macroregiao.csv", index=False)
                        import seaborn as sns, matplotlib.pyplot as plt
                        plt.figure(figsize=(10,6))
                        sns.barplot(data=grp, x="macroregiao_label", y="WAPE_mean")
                        plt.xticks(rotation=20)
                        plt.title("WAPE por Macro-região - Holdout")
                        plt.tight_layout()
                        plt.savefig(out_plots / "segmentation_wape_by_macroregiao.png", dpi=150, bbox_inches="tight")
                        plt.close()
                        # Experimental interactive versions (Plotly / Altair / Bokeh)
                        # Plotly
                        try:
                            import plotly.express as px
                            fig = px.bar(grp, x="macroregiao_label", y="WAPE_mean", title="WAPE por Macro-região - Holdout")
                            fig.write_html(str(out_plots_exp / "segmentation_wape_by_macroregiao_plotly.html"))
                        except Exception:
                            pass
                        # Altair
                        try:
                            import altair as alt
                            alt.data_transformers.disable_max_rows()
                            chart = (
                                alt.Chart(grp)
                                .mark_bar()
                                .encode(
                                    x=alt.X("macroregiao_label:N", title="Macro-região"),
                                    y=alt.Y("WAPE_mean:Q", title="WAPE"),
                                    tooltip=["macroregiao_label:N", alt.Tooltip("WAPE_mean:Q", format=",.4f")]
                                )
                                .properties(title="WAPE por Macro-região - Holdout")
                                .interactive()
                            )
                            chart.save(str(out_plots_exp / "segmentation_wape_by_macroregiao_altair.html"))
                        except Exception:
                            pass
                        # Bokeh
                        try:
                            from bokeh.plotting import figure
                            from bokeh.models import ColumnDataSource, HoverTool
                            from bokeh.io import save
                            from bokeh.resources import CDN
                            src = ColumnDataSource(grp)
                            p = figure(x_range=list(grp["macroregiao_label"].astype(str).unique()),
                                       title="WAPE por Macro-região - Holdout",
                                       sizing_mode="stretch_width", height=400)
                            p.vbar(x="macroregiao_label", top="WAPE_mean", width=0.6, source=src)
                            p.add_tools(HoverTool(tooltips=[("Macro-região","@macroregiao_label"), ("WAPE","@WAPE_mean{0.0000}")]))
                            save(p, filename=str(out_plots_exp / "segmentation_wape_by_macroregiao_bokeh.html"), resources=CDN)
                        except Exception:
                            pass
        # Blocos executivos: Top/Bottom 20% por receita (definido no treino)
            except Exception:
                pass

        try:
            if len(df_train) > 0 and len(df_holdout) > 0 and results:
                res_df = pd.DataFrame(results)
                best_model_name = res_df.iloc[0]["model"] if not res_df.empty else None
                if best_model_name:
                    te_train, _ = make_te_features(df_train, df_train["valor"].values, df_train)
                    X_train_full = np.hstack([df_train[feat_cols].values, te_train]) if te_train.size else df_train[feat_cols].values
                    best_est = None
                    for name, est in grids:
                        if name == best_model_name:
                            best_est = est
                            break
                    if best_est is not None:
                        best_est.fit(X_train_full, df_train["valor"].values)
                        te_hold, _ = make_te_features(df_train, df_train["valor"].values, df_holdout)
                        X_hold_full = np.hstack([df_holdout[feat_cols].values, te_hold]) if te_hold.size else df_holdout[feat_cols].values
                        pred_hold = best_est.predict(X_hold_full)
                        df_eval = df_holdout.copy()
                        df_eval["pred"] = pred_hold
                        df_eval["WAPE"] = np.abs(df_eval["valor"] - df_eval["pred"]) / (np.abs(df_eval["valor"]) + 1e-9)
                        # Receita por loja no TREINO
                        rev_train = df_train.groupby("id_loja")["valor"].sum().rename("rev_train")
                        df_eval = df_eval.merge(rev_train, on="id_loja", how="left")
                        q80 = df_eval["rev_train"].quantile(0.8)
                        q20 = df_eval["rev_train"].quantile(0.2)
                        df_eval["rev_segment"] = np.where(df_eval["rev_train"] >= q80, "Top20%",
                                                    np.where(df_eval["rev_train"] <= q20, "Bottom20%", "Middle60%"))
                        seg = df_eval.groupby("rev_segment").agg(WAPE_mean=("WAPE","mean")).reset_index()
                        seg.to_csv(out_tables / "top_bottom_20_performance.csv", index=False)
                        import matplotlib.pyplot as plt, seaborn as sns
                        plt.figure(figsize=(8,5))
                        sns.barplot(data=seg, x="rev_segment", y="WAPE_mean", order=["Top20%","Middle60%","Bottom20%"])
                        plt.title("WAPE por segmento de receita (definido no treino)")
                        plt.tight_layout()
                        plt.savefig(out_plots / "segmentation_wape_by_revenue_quintile.png", dpi=150, bbox_inches="tight")
                        plt.close()
                        # Experimental interactive version (Plotly)
                        try:
                            import plotly.express as px
                            fig = px.bar(seg, x="rev_segment", y="WAPE_mean",
                                         category_orders={"rev_segment":["Top20%","Middle60%","Bottom20%"]},
                                         title="WAPE por segmento de receita (definido no treino)")
                            fig.write_html(str(out_plots_exp / "segmentation_wape_by_revenue_quintile.html"))
                        except Exception:
                            pass
        except Exception:
            pass

        # Ticket médio: modelo simples de previsão de qtd e avaliação de ticket
        try:
            if "qtd" in dfm.columns and len(df_holdout) > 0 and results:
                from sklearn.ensemble import RandomForestRegressor
                # Treina modelo de qtd com mesmas features + TE
                yq = df_train["qtd"].values if "qtd" in df_train.columns else None
                if yq is not None and len(yq) == len(df_train):
                    te_train_q, _ = make_te_features(df_train, df_train["valor"].values, df_train)
                    X_train_q = np.hstack([df_train[feat_cols].values, te_train_q]) if te_train_q.size else df_train[feat_cols].values
                    te_hold_q, _ = make_te_features(df_train, df_train["valor"].values, df_holdout)
                    X_hold_q = np.hstack([df_holdout[feat_cols].values, te_hold_q]) if te_hold_q.size else df_holdout[feat_cols].values
                    qmodel = RandomForestRegressor(n_estimators=150, random_state=42)
                    qmodel.fit(X_train_q, yq)
                    qtd_pred = np.clip(qmodel.predict(X_hold_q), 0, None)
                    # Reusar melhor modelo de valor já treinado acima quando possível
                    # Se não disponível neste bloco, refita rapidamente
                    res_df = pd.DataFrame(results)
                    best_model_name = res_df.iloc[0]["model"] if not res_df.empty else None
                    if best_model_name:
                        te_train_v, _ = make_te_features(df_train, df_train["valor"].values, df_train)
                        X_train_v = np.hstack([df_train[feat_cols].values, te_train_v]) if te_train_v.size else df_train[feat_cols].values
                        best_est = None
                        for name, est in grids:
                            if name == best_model_name:
                                best_est = est
                                break
                        if best_est is not None:
                            best_est.fit(X_train_v, df_train["valor"].values)
                            te_hold_v, _ = make_te_features(df_train, df_train["valor"].values, df_holdout)
                            X_hold_v = np.hstack([df_holdout[feat_cols].values, te_hold_v]) if te_hold_v.size else df_holdout[feat_cols].values
                            valor_pred = np.clip(best_est.predict(X_hold_v), 0, None)
                            # Métricas de ticket
                            eps = 1e-9
                            ticket_real = df_holdout["valor"].values / (df_holdout.get("qtd", pd.Series(np.nan, index=df_holdout.index)).values + eps)
                            ticket_pred = valor_pred / (qtd_pred + eps)
                            ticket_mae = np.nanmean(np.abs(ticket_real - ticket_pred))
                            # sMAPE ticket
                            smape_ticket = 2.0 * np.nanmean(np.abs(ticket_pred - ticket_real) / (np.abs(ticket_pred) + np.abs(ticket_real) + eps))
                            pd.DataFrame({
                                "ticket_MAE":[ticket_mae],
                                "ticket_sMAPE":[smape_ticket]
                            }).to_csv(out_tables / "ticket_accuracy_holdout.csv", index=False)
                            # Segmentação por macro-região do erro de ticket
                            if "macroregiao" in df_holdout.columns:
                                df_t = df_holdout.copy()
                                df_t["ticket_real"] = ticket_real
                                df_t["ticket_pred"] = ticket_pred
                                df_t["ticket_abs_err"] = np.abs(df_t["ticket_real"] - df_t["ticket_pred"])
                                tseg = df_t.groupby("macroregiao")["ticket_abs_err"].mean().reset_index()
                                tseg.to_csv(out_tables / "ticket_error_by_macroregiao.csv", index=False)
                                import matplotlib.pyplot as plt, seaborn as sns
                                plt.figure(figsize=(10,6))
                                sns.barplot(data=tseg, x="macroregiao", y="ticket_abs_err")
                                plt.title("Erro médio absoluto do ticket por Macro-região (Holdout)")
                                plt.tight_layout()
                                plt.savefig(out_plots / "segmentation_ticket_by_macroregiao.png", dpi=150, bbox_inches="tight")
                                plt.close()
        except Exception:
            pass

        # Testes de significância regional (Kruskal-Wallis + pairwise Mann-Whitney com Bonferroni)
        try:
            if len(df_holdout) > 0 and results and "macroregiao" in df_holdout.columns:
                # Distribuição de WAPE por loja em holdout
                res_df = pd.DataFrame(results)
                best_model_name = res_df.iloc[0]["model"] if not res_df.empty else None
                if best_model_name:
                    te_train, _ = make_te_features(df_train, df_train["valor"].values, df_train)
                    X_train_full = np.hstack([df_train[feat_cols].values, te_train]) if te_train.size else df_train[feat_cols].values
                    best_est = None
                    for name, est in grids:
                        if name == best_model_name:
                            best_est = est
                            break
                    if best_est is not None:
                        best_est.fit(X_train_full, df_train["valor"].values)
                        te_hold, _ = make_te_features(df_train, df_train["valor"].values, df_holdout)
                        X_hold_full = np.hstack([df_holdout[feat_cols].values, te_hold]) if te_hold.size else df_holdout[feat_cols].values
                        pred_hold = best_est.predict(X_hold_full)
                        df_eval = df_holdout.copy()
                        df_eval["pred"] = pred_hold
                        df_eval["abs_err"] = np.abs(df_eval["valor"] - df_eval["pred"])
                        # Agrega WAPE por loja (evita viés de nº de dias por loja)
                        tmp = df_eval.groupby(["id_loja","macroregiao"]).apply(lambda d: np.sum(np.abs(d["valor"]-d["pred"]))/(np.sum(np.abs(d["valor"]))+1e-9)).reset_index(name="WAPE_store")
                        groups = [g["WAPE_store"].values for _, g in tmp.groupby("macroregiao") if len(g) > 0]
                        if len(groups) >= 2 and all(len(g)>1 for g in groups):
                            try:
                                from scipy.stats import kruskal, mannwhitneyu
                                H, p = kruskal(*groups)
                                rows = [{"test":"kruskal","H":H,"p_value":p}]
                                # pairwise
                                regs = [r for r,_ in tmp.groupby("macroregiao")]
                                m = len(regs)
                                for i in range(m):
                                    for j in range(i+1,m):
                                        a = tmp.loc[tmp["macroregiao"]==regs[i],"WAPE_store"].values
                                        b = tmp.loc[tmp["macroregiao"]==regs[j],"WAPE_store"].values
                                        if len(a)>1 and len(b)>1:
                                            stat, p2 = mannwhitneyu(a,b, alternative="two-sided")
                                            rows.append({"pair":f"{regs[i]} vs {regs[j]}","test":"mannwhitney","stat":stat,"p_value_raw":p2,"p_value_bonf":min(1.0,p2*m*(m-1)/2)})
                                pd.DataFrame(rows).to_csv(out_tables / "regional_significance_tests.csv", index=False)
                            except Exception:
                                # fallback simples: salvar estatísticas descritivas
                                desc = tmp.groupby("macroregiao")["WAPE_store"].agg(["count","mean","std"]).reset_index()
                                desc.to_csv(out_tables / "regional_significance_tests.csv", index=False)
        except Exception:
            pass

        for m_cv, m_hold in [("CV_WAPE","HOLDOUT_WAPE"),("CV_sMAPE","HOLDOUT_sMAPE"),("CV_MAE","HOLDOUT_MAE"),("CV_RMSE","HOLDOUT_RMSE")]:
            if not results:
                break
            res_df = pd.DataFrame(results)
            if m_cv in res_df.columns and m_hold in res_df.columns:
                plt.figure(figsize=(12,6))
                plot_df = res_df[["model", m_cv, m_hold]].melt("model", var_name="split", value_name="value")
                sns.barplot(data=plot_df, x="model", y="value", hue="split")
                plt.xticks(rotation=90)
                plt.title(f"Comparação de {m_cv.replace('CV_','')} (CV vs Holdout)")
                plt.tight_layout()
                plt.savefig(out_plots / f"compare_{m_cv.replace('CV_','').lower()}.png", dpi=150, bbox_inches="tight")
                plt.close()
                # Experimental interactive versions (Plotly / Altair / Bokeh)
                # Plotly
                try:
                    import plotly.express as px
                    fig = px.bar(plot_df, x="model", y="value", color="split",
                                 title=f"Comparação de {m_cv.replace('CV_','')} (CV vs Holdout)")
                    fig.write_html(str(out_plots_exp / f"compare_{m_cv.replace('CV_','').lower()}_plotly.html"))
                except Exception:
                    pass
                # Altair
                try:
                    import altair as alt
                    alt.data_transformers.disable_max_rows()
                    chart = (
                        alt.Chart(plot_df)
                        .mark_bar()
                        .encode(
                            x=alt.X("model:N", sort=None, title="Modelo"),
                            y=alt.Y("value:Q", title="Valor"),
                            color=alt.Color("split:N", title="Divisão"),
                            tooltip=["model:N", "split:N", alt.Tooltip("value:Q", format=",.4f")]
                        )
                        .properties(title=f"Comparação de {m_cv.replace('CV_','')} (CV vs Holdout)")
                        .interactive()
                    )
                    chart.save(str(out_plots_exp / f"compare_{m_cv.replace('CV_','').lower()}_altair.html"))
                except Exception:
                    pass
                # Bokeh
                try:
                    from bokeh.plotting import figure
                    from bokeh.models import ColumnDataSource, HoverTool
                    from bokeh.io import save
                    from bokeh.resources import CDN
                    src = ColumnDataSource(plot_df)
                    p = figure(x_range=list(plot_df["model"].astype(str).unique()),
                               title=f"Comparação de {m_cv.replace('CV_','')} (CV vs Holdout)",
                               sizing_mode="stretch_width", height=400, toolbar_location="right")
                    # Plot groups per split
                    for sp in plot_df["split"].unique():
                        sdf = plot_df[plot_df["split"]==sp]
                        ssrc = ColumnDataSource(sdf)
                        p.vbar(x="model", top="value", width=0.4, source=ssrc, legend_label=str(sp))
                    p.add_tools(HoverTool(tooltips=[("Modelo","@model"), ("Split","@split"), ("Valor","@value{0.0000}")]))
                    p.legend.location = "top_left"
                    save(p, filename=str(out_plots_exp / f"compare_{m_cv.replace('CV_','').lower()}_bokeh.html"), resources=CDN)
                except Exception:
                    pass
        # Uplift vs baseline (se baseline disponível): usa média dos baselines por loja
        if not base_df.empty and results:
            res_df = pd.DataFrame(results)
            if "HOLDOUT_WAPE" in res_df.columns:
                base_agg = base_df.groupby("model")["WAPE"].mean().to_dict()
                chosen = next((c for c in ["baseline_seasonal_naive","baseline_mean7","baseline_naive"] if c in base_agg), None)
                if chosen:
                    base_wape = base_agg[chosen]
                    res_df["uplift_vs_baseline_%"] = (base_wape - res_df["HOLDOUT_WAPE"]) / base_wape * 100.0
                    plt.figure(figsize=(12,6))
                    sns.barplot(data=res_df, x="model", y="uplift_vs_baseline_%")
                plt.axhline(0, color='k', linestyle='--')
                plt.xticks(rotation=90)
                plt.title(f"Uplift % vs {chosen}")
                plt.tight_layout()
                plt.savefig(out_plots / f"uplift_vs_baseline_{chosen}.png", dpi=150, bbox_inches="tight")
                plt.close()
                # Experimental interactive versions (Plotly / Altair / Bokeh)
                # Plotly
                try:
                    import plotly.express as px
                    fig = px.bar(res_df, x="model", y="uplift_vs_baseline_%",
                                 title=f"Uplift % vs {chosen}")
                    fig.add_hline(y=0)
                    fig.write_html(str(out_plots_exp / f"uplift_vs_baseline_{chosen}_plotly.html"))
                except Exception:
                    pass
                # Altair
                try:
                    import altair as alt
                    alt.data_transformers.disable_max_rows()
                    chart = (
                        alt.Chart(res_df)
                        .mark_bar()
                        .encode(
                            x=alt.X("model:N", sort=None, title="Modelo"),
                            y=alt.Y("uplift_vs_baseline_%:Q", title="Uplift %"),
                            tooltip=["model:N", alt.Tooltip("uplift_vs_baseline_%:Q", format=",.2f")]
                        )
                        .properties(title=f"Uplift % vs {chosen}")
                        .interactive()
                    )
                    chart.save(str(out_plots_exp / f"uplift_vs_baseline_{chosen}_altair.html"))
                except Exception:
                    pass
                # Bokeh
                try:
                    from bokeh.plotting import figure
                    from bokeh.models import ColumnDataSource, HoverTool
                    from bokeh.io import save
                    from bokeh.resources import CDN
                    src = ColumnDataSource(res_df)
                    p = figure(x_range=list(res_df["model"].astype(str).unique()),
                               title=f"Uplift % vs {chosen}",
                               sizing_mode="stretch_width", height=400)
                    p.vbar(x="model", top="uplift_vs_baseline_%", width=0.6, source=src)
                    p.add_tools(HoverTool(tooltips=[("Modelo","@model"), ("Uplift %","@{uplift_vs_baseline_%}{0.00}")]))
                    save(p, filename=str(out_plots_exp / f"uplift_vs_baseline_{chosen}_bokeh.html"), resources=CDN)
                except Exception:
                    pass
        # Performance segmentation by UF and Tipo_PDV (if present and sufficient)
        if {"uf_label","Tipo_PDV_label"}.issubset(dfm.columns) and len(df_holdout) > 0:
            seg_cols = []
            if "uf_label" in dfm.columns: seg_cols.append("uf_label")
            if "Tipo_PDV_label" in dfm.columns: seg_cols.append("Tipo_PDV_label")
            best_model_name = None
            if results:
                res_df = pd.DataFrame(results)
                if not res_df.empty:
                    best_model_name = res_df.iloc[0]["model"]
            if best_model_name:
                # Refit best model on train full with TE
                te_train, te_names = make_te_features(df_train, df_train["valor"].values, df_train)
                X_train_full = np.hstack([df_train[feat_cols].values, te_train]) if te_train.size else df_train[feat_cols].values
                best_est = None
                for name, est in grids:
                    if name == best_model_name:
                        best_est = est
                        break
                if best_est is not None:
                    best_est.fit(X_train_full, df_train["valor"].values)
                    te_hold, _ = make_te_features(df_train, df_train["valor"].values, df_holdout)
                    X_hold_full = np.hstack([df_holdout[feat_cols].values, te_hold]) if te_hold.size else df_holdout[feat_cols].values
                    pred_hold = best_est.predict(X_hold_full)
                    df_eval = df_holdout.copy()
                    df_eval["pred"] = pred_hold
                    df_eval["WAPE"] = np.abs(df_eval["valor"] - df_eval["pred"]) / (np.abs(df_eval["valor"]) + 1e-9)
                    for sc in seg_cols:
                        grp = df_eval.groupby(sc).agg(WAPE_mean=("WAPE","mean"), MAE=(lambda x: x, "mean")).reset_index()
                        plt.figure(figsize=(12,6))
                        sns.barplot(data=grp, x=sc, y="WAPE_mean")
                        plt.xticks(rotation=45)
                        plt.title(f"WAPE por {sc} - Holdout")
                        plt.tight_layout()
                        plt.savefig(out_plots / f"segmentation_wape_by_{sc}.png", dpi=150, bbox_inches="tight")
                        plt.close()
    except Exception:
        pass


        cv_df = pd.DataFrame(fold_metrics)
        # store per-model CV details and save CSV for later visualizations
        try:
            if not cv_df.empty:
                cv_details[model_name] = cv_df.copy()
                cv_df.assign(model=model_name).to_csv(out_tables / f"cv_folds_{model_name}.csv", index=False)
        except Exception:
            pass
        res = {
            "model": model_name,
            "CV_WAPE": cv_df["WAPE"].mean() if not cv_df.empty else np.nan,
            "CV_sMAPE": cv_df["sMAPE"].mean() if not cv_df.empty else np.nan,
            "CV_MAE": cv_df["MAE"].mean() if not cv_df.empty else np.nan,
            "CV_RMSE": cv_df["RMSE"].mean() if not cv_df.empty else np.nan,
        }

        # Fit on full train and evaluate on holdout
        try:
            # Fit target encoding on full train and apply to both train and holdout
            te_train, te_names = make_te_features(df_train, df_train["valor"].values, df_train)
            te_hold, _ = make_te_features(df_train, df_train["valor"].values, df_holdout)
            X_train_full = np.hstack([df_train[feat_cols].values, te_train]) if te_train.size else df_train[feat_cols].values
            X_hold_full = np.hstack([df_holdout[feat_cols].values, te_hold]) if te_hold.size else df_holdout[feat_cols].values

            model.fit(X_train_full, df_train["valor"].values)
            hold_pred = model.predict(X_hold_full)
            res.update({
                "HOLDOUT_WAPE": wape(df_holdout["valor"].values, hold_pred),
                "HOLDOUT_sMAPE": smape(df_holdout["valor"].values, hold_pred),
                "HOLDOUT_MAE": mean_absolute_error(df_holdout["valor"].values, hold_pred),
                "HOLDOUT_RMSE": mean_squared_error(df_holdout["valor"].values, hold_pred, squared=False),
            })
        except Exception:
            pass

    # After all models: generate enhanced comparative visualizations
    try:
        res_df = pd.DataFrame(results)
        if not res_df.empty:
            # Derive family labels for grouping
            def model_family(name: str) -> str:
                n = name.lower()
                if n.startswith("rf_") or n.startswith("xgb_") or n.startswith("lgb_") or n.startswith("gbr_"):
                    return "tree-based"
                if n.startswith("ridge") or n.startswith("lasso") or n.startswith("pca_ridge"):
                    return "linear"
                if n.startswith("svr_"):
                    return "kernel"
                if n.startswith("mlp_"):
                    return "neural"
                if n.startswith("kmeanslbl") or n.startswith("dbscanlbl") or n.startswith("isoforest"):
                    return "unsup-augmented"
                return "other"
            res_df["family"] = res_df["model"].apply(model_family)

            # Cross-model performance matrix (CV metrics)
            for metric in ["CV_WAPE","CV_sMAPE","CV_MAE","CV_RMSE"]:
                if metric in res_df.columns:
                    pivot = res_df.pivot_table(index="family", columns="model", values=metric, aggfunc="first")
                    plt.figure(figsize=(min(18, 4 + 0.35*max(1,len(pivot.columns))), 6))
                    sns.heatmap(pivot, annot=False, cmap="viridis", cbar_kws={"label":metric})
                    plt.title(f"Matriz de performance ({metric}) por família x modelo")
                    plt.tight_layout()
                    plt.savefig(out_plots / f"matrix_{metric.lower()}.png", dpi=150, bbox_inches="tight")
                    plt.close()

            # Best performer por família (ordenado por HOLDOUT_WAPE se existir senão CV_WAPE)
            order_col = "HOLDOUT_WAPE" if "HOLDOUT_WAPE" in res_df.columns else "CV_WAPE"
            fam_best = res_df.sort_values(order_col, ascending=True, na_position="last").groupby("family").head(1)
            plt.figure(figsize=(10,5))
            sns.barplot(data=fam_best, x="family", y=order_col)
            plt.title(f"Melhor modelo por família ({order_col})")
            plt.tight_layout()
            plt.savefig(out_plots / f"best_by_family_{order_col.lower()}.png", dpi=150, bbox_inches="tight")
            plt.close()

            # Distribuições de performance (box/violin) pelos folds de CV
            # Agregar arquivos cv_folds_*.csv se existirem
            fold_files = list(out_tables.glob("cv_folds_*.csv"))
            if fold_files:
                all_cv = []
                for fp in fold_files:
                    try:
                        cdf = pd.read_csv(fp)
                        cdf["model"] = fp.stem.replace("cv_folds_", "") if "model" not in cdf.columns else cdf["model"]
                        cdf["family"] = cdf["model"].apply(model_family)
                        all_cv.append(cdf)
                    except Exception:
                        pass
                if all_cv:
                    cvcat = pd.concat(all_cv, ignore_index=True)
                    for metric in ["WAPE","sMAPE","MAE","RMSE"]:
                        if metric in cvcat.columns and not cvcat.empty:
                            plt.figure(figsize=(14,6))
                            sns.boxplot(data=cvcat, x="family", y=metric)
                            plt.title(f"Distribuição de {metric} (CV) por família")
                            plt.tight_layout()
                            plt.savefig(out_plots / f"cv_distribution_box_{metric.lower()}.png", dpi=150, bbox_inches="tight")
                            plt.close()
                            # Violin
                            plt.figure(figsize=(14,6))
                            sns.violinplot(data=cvcat, x="family", y=metric, inner="quartile", cut=0)
                            plt.title(f"Distribuição de {metric} (CV) por família - Violin")
                            plt.tight_layout()
                            plt.savefig(out_plots / f"cv_distribution_violin_{metric.lower()}.png", dpi=150, bbox_inches="tight")
                            plt.close()

            # Significância entre top-3 modelos (por coluna de ordenação)
            try:
                from scipy.stats import mannwhitneyu
                if fold_files:
                    # usar cvcat de cima se existir
                    if 'cvcat' in locals() and not cvcat.empty:
                        top_models = res_df.sort_values(order_col, ascending=True, na_position="last").head(3)["model"].tolist()
                        pairs = []
                        pvals = []
                        for i in range(len(top_models)):
                            for j in range(i+1, len(top_models)):
                                a = cvcat.loc[cvcat["model"]==top_models[i], "WAPE"].dropna().values
                                b = cvcat.loc[cvcat["model"]==top_models[j], "WAPE"].dropna().values
                                if len(a)>1 and len(b)>1:
                                    stat, p = mannwhitneyu(a,b, alternative="two-sided")
                                    pairs.append(f"{top_models[i]} vs {top_models[j]}")
                                    pvals.append(p)
                        if pairs:
                            sig_df = pd.DataFrame({"pair":pairs, "p_value":pvals})
                            sig_df.to_csv(out_tables / "cv_top_models_significance.csv", index=False)
                            plt.figure(figsize=(8,3+0.3*len(pairs)))
                            sns.barplot(data=sig_df, x="p_value", y="pair")
                            plt.axvline(0.05, color='r', linestyle='--', label='0.05')
                            plt.legend()
                            plt.title("Teste de significância (Mann-Whitney) WAPE entre Top-3 modelos (CV)")
                            plt.tight_layout()
                            plt.savefig(out_plots / "cv_significance_top3.png", dpi=150, bbox_inches="tight")
                            plt.close()
            except Exception:
                pass

            # Importâncias/coeficientes comparativos (quando aplicável)
            try:
                # Reajustar modelos interpretáveis no treino completo
                te_train, te_names = make_te_features(df_train, df_train["valor"].values, df_train)
                X_train_full = np.hstack([df_train[feat_cols].values, te_train]) if te_train.size else df_train[feat_cols].values
                interpretable = []
                for name, est in grids:
                    nm = name.lower()
                    try:
                        if nm.startswith("rf_") or nm.startswith("gbr_"):
                            est.fit(X_train_full, df_train["valor"].values)
                            if hasattr(est, "feature_importances_"):
                                interpretable.append((name, est.feature_importances_))
                        elif nm.startswith("ridge") or nm.startswith("lasso"):
                            est.fit(X_train_full, df_train["valor"].values)
                            if hasattr(est, "coef_"):
                                interpretable.append((name, np.abs(est.coef_).ravel()))
                    except Exception:
                        continue
                if interpretable:
                    # construir nomes de features (originais + TE)
                    f_names = feat_cols + (te_names if te_train.size else [])
                    # normalizar para comparação
                    mat = []
                    labels = []
                    for nm, imp in interpretable:
                        v = np.asarray(imp).reshape(-1)
                        if len(v) != len(f_names):
                            continue
                        v = v / (np.sum(np.abs(v)) + 1e-9)
                        mat.append(v)
                        labels.append(nm)
                    if mat:
                        M = np.vstack(mat)
                        plt.figure(figsize=(min(18, 4 + 0.3*len(labels)), 18), num=None)
                        sns.heatmap(pd.DataFrame(M, index=labels, columns=f_names), cmap="mako")
                        plt.title("Comparação de importâncias/coeficientes (normalizados)")
                        plt.tight_layout()
                        plt.savefig(out_plots / "feature_importance_comparison.png", dpi=150, bbox_inches="tight")
                        plt.close()
            except Exception:
                pass
    except Exception:
        pass

        results.append(res)

    # Aggregate results
    res_df = pd.DataFrame(results)
    if not res_df.empty:
        sort_cols = [c for c in ["HOLDOUT_WAPE", "CV_WAPE"] if c in res_df.columns]
        if sort_cols:
            res_df = res_df.sort_values(sort_cols, ascending=True, na_position="last")
    res_df.to_csv(out_tables / "model_benchmark.csv", index=False)

    # Simple ranking and recommendation
    if not res_df.empty:
        best = res_df.iloc[0]
        (out_tables / "model_recommendation.txt").write_text(
            f"Best model: {best['model']}\nCV_WAPE={best['CV_WAPE']:.4f} HOLDOUT_WAPE={best['HOLDOUT_WAPE']:.4f}\n"
        )

        # SHAP real if tree-based and shap available
        try:
            import shap
            best_model = None
            for name, model in grids:
                if name == best["model"]:
                    best_model = model
                    break
            if best_model is not None and hasattr(best_model, "fit"):
                best_model.fit(df_train[feat_cols].values, df_train["valor"].values)
                explainer = shap.Explainer(best_model, df_train[feat_cols].values)
                shap_values = explainer(df_holdout[feat_cols].values[:512])
                # Save SHAP summary plot
                shap.summary_plot(shap_values, df_holdout[feat_cols].values[:512], show=False)
                import matplotlib.pyplot as plt
                plt.tight_layout()
                shap_path = out_plots / "shap_summary.png"
                plt.savefig(shap_path, dpi=150, bbox_inches="tight")
                plt.close()
        except Exception:
            pass

    return {
        "features": feat_cols,
        "results_path": str(out_tables / "model_benchmark.csv"),
    }


if __name__ == "__main__":
    BASE = Path.cwd()
    reports_root = BASE / "reports" / "2025-08-15"
    data_clean = BASE / "data" / "clean" / "cleaned.csv"
    if data_clean.exists():
        dfc = pd.read_csv(data_clean, parse_dates=["data"])
        run_model_comparison(dfc, reports_root)
    else:
        print("data/clean/cleaned.csv not found.")

