# Relatórios Gerados

Lista dos artefatos atualmente disponíveis no projeto InfoPepper.

## Visualizações (`reports/figures/`)

### Análises Temporais

- `serie_diaria_total.png`: Série temporal da receita total diária
- `bar_dow.png`: Receita por dia da semana
- `bar_ci_dow.png`: Receita por dia da semana com intervalos de confiança

### Análises Geográficas

- `heatmap_revenue_by_state.png`: Heatmap de receita por estado
- `heatmap_revenue_by_city.png`: Heatmap de receita por cidade
- `ranking_uf.png`: Ranking de estados por receita
- `state_performance_matrix.png`: Matriz de performance por estado
- `top_cities_revenue_detailed.png`: Detalhamento das principais cidades

### Análises de Lojas

- `top_stores.png`: Ranking das principais lojas
- `scatter_outliers.png`: Análise de outliers nas lojas

### Análises Estatísticas

- `heatmap_corr.png`: <PERSON><PERSON>rrel<PERSON> (Spearman)
- `heatmap_icl.png`: Heatmap de análise ICL
- `revenue_distribution_analysis.png`: Análise de distribuição de receita

### Modelagem e Machine Learning

- `feature_importance.png`: Importância das features do modelo
- `real_vs_pred_samples.png`: Comparação valores reais vs preditos
- `shap_summary_sample.png`: Resumo da interpretabilidade SHAP

## Tabelas (`reports/tables/`)

_Pasta vazia - tabelas são geradas durante a execução do notebook_

## Exports (`reports/exports/`)

_Pasta vazia - preparada para relatórios finais exportados_

## Observações

- Todos os arquivos listados estão atualmente disponíveis no projeto
- As visualizações foram geradas pelo notebook principal de análise
- Novos artefatos podem ser adicionados conforme evolução do projeto
