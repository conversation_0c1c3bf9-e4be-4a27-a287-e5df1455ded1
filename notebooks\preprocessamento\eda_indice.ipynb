{"cells": [{"cell_type": "markdown", "id": "f7f48a14", "metadata": {}, "source": ["# Índice de EDA (Preprocessamento)\n", "\n", "Este índice organiza os notebooks de EDA e define a ordem recomendada de execução. Todos utilizam o dataset limpo em `data/clean/cleaned_featured.csv` (ou equivalente em `data/processed/`).\n", "\n", "## Or<PERSON><PERSON> recomendada\n", "1. [eda_distribuicoes (antigo 01_data_exploration_analysis)](eda_distribuicoes.ipynb) — distribuições, descritas, outliers, correlações iniciais.\n", "\n", "2. [eda_geografico (antigo 04_territorial_analysis)](eda_geografico.ipynb) — padrões por UF/cidade, mapas e sumários regionais.\n", "\n", "## Observações\n", "- Exporte figuras para `reports/2025-08-15/plots/eda/`.\n", "- Tabelas auxiliares devem ir para `reports/2025-08-15/tables/`.\n", "- Man<PERSON><PERSON> títulos, labels e legendas em todos os gráficos (consistência visual).\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}