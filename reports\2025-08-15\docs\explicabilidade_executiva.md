# Explicabilidade Executiva — Modelos Supervisionado e Clustering Territorial

Este documento resume as variáveis usadas, a lógica de explicação dos modelos e a interpretação de resultados para decisões territoriais e de segmentação.


## 1. Catálogo de Features

- Catálogo salvo em: reports/2025-08-15/tables/feature_catalog.csv (colunas: feature, dtype, na_rate, estatísticas)

- Relacionamentos salvo em: reports/2025-08-15/tables/feature_relationships.csv

## 2. Análise de Explicabilidade

- Importância de features (RF): imagens/explain_feature_importance_rf_v1.png
- Correlação entre principais features: imagens/features_corr_heatmap_v1.png
- Impacto geográfico no clustering territorial evidenciado por:
  - clusters_territorial_distribution_v1.png, clusters_territorial_silhouette_box_v1.png, territorial_map_geo_clusters_v1.png

## 3. Sumários dos Modelos

- Supervisionado: use os gráficos já gerados (curvas de aprendizado, resíduos, predito vs real)
- Clustering: comparação K=5 vs K=7 em clusters_quality_comparison_v1.png e matriz de correspondência clusters_k5_vs_k7_confusion_v1.png (se disponível)

## 4. Métricas com interpretação executiva

- Supervisionado: R², RMSE, MAE — maior R² e menores RMSE/MAE indicam previsões mais confiáveis para metas e orçamento.
- Não supervisionado: Silhouette — K=5≈0.132 vs K=7≈0.378 (quanto maior, melhor coesão/separação).

## 5. Recomendações

- Usar os clusters territoriais (K=7) para “onde atuar” e o modelo supervisionado para “quanto atuar”.
- Se necessário restringir para 5 segmentos, agrupar SC com PR/RS (Sul) e decidir se SP fica separado.