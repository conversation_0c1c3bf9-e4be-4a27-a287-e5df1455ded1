import os
import re
import shutil
import subprocess
from pathlib import Path
import unicodedata
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

BASE = Path(__file__).resolve().parents[1]
# Prefer enriched dataset with UF if available
DATA_PATH_ENRICHED = BASE / 'data' / 'processed' / 'features_engineered_regional.csv'
DATA_PATH_FALLBACK = BASE / 'data' / 'processed' / 'features_engineered.csv'
DATA_PATH = DATA_PATH_ENRICHED if DATA_PATH_ENRICHED.exists() else DATA_PATH_FALLBACK
REPORTS_BASE = BASE / 'reports' / '2025-08-15'
RUNS_BASE = BASE / 'regional_runs'

np.random.seed(42)


def normalize_text(s: str) -> str:
    if s is None:
        return ''
    s = str(s)
    s = unicodedata.normalize('NFKD', s).encode('ascii', 'ignore').decode('ascii')
    return s.lower().strip()


def detect_uf_column(df: pd.DataFrame) -> str | None:
    # Prefer 'uf' exactly; fallback: any column containing 'uf' or 'estado' or 'state'
    candidates = []
    for c in df.columns:
        cl = normalize_text(c)
        if cl == 'uf':
            return c
        if any(k in cl for k in ['uf', 'estado', 'state']):
            candidates.append(c)
    # Prefer shorter names
    return candidates[0] if candidates else None


def filter_sp(df: pd.DataFrame) -> pd.Series:
    # Priority 1: explicit one-hot column for SP region if available
    for candidate in [
        'Dim_Lojas.REGIAO_CHILLI_SÃO PAULO',
        'Dim_Lojas.REGIAO_CHILLI_SAO PAULO',
        'REGIAO_CHILLI_SÃO PAULO',
        'REGIAO_CHILLI_SAO PAULO',
    ]:
        if candidate in df.columns:
            try:
                col = df[candidate]
                if col.dtype.kind in 'biufc':
                    return (col > 0).astype(bool)
                return col.astype(str).str.strip().isin(['1','True','true','SIM','Yes'])
            except Exception:
                pass
    # Build boolean mask for Sao Paulo using multiple cues
    mask = pd.Series(False, index=df.index)
    uf_col = detect_uf_column(df)
    if uf_col:
        vals = df[uf_col].astype(str).fillna('')
        vals_norm = vals.apply(normalize_text)
        # Accept 'sp', 'sao paulo', 'são paulo'
        mask = mask | vals_norm.str.fullmatch(r'(sp|sao paulo|sao\s*paulo|sao\-paulo|sao_paulo)')
    # Additional hints from other columns
    for col in df.columns:
        cl = normalize_text(col)
        if any(k in cl for k in ['cidade', 'municipio', 'estado', 'uf', 'local', 'regiao']):
            vals_norm = df[col].astype(str).fillna('').apply(normalize_text)
            cond = vals_norm.str.contains(r'(^|\b)(sao\s*paulo|sao\-paulo|sao_paulo|\bsp\b)($|\b)')
            mask = mask | cond
    return mask


def prepare_region_run(df: pd.DataFrame, name: str) -> Path:
    run_dir = RUNS_BASE / name
    data_dir = run_dir / 'data' / 'processed'
    models_dir = run_dir / 'models'
    reports_dir = run_dir / 'reports' / '2025-08-15'
    for d in [data_dir, models_dir, reports_dir]:
        d.mkdir(parents=True, exist_ok=True)
    # Save subset
    out_csv = data_dir / 'features_engineered.csv'
    df.to_csv(out_csv, index=False)
    return run_dir


def run_notebook(nb_rel_path: str, run_dir: Path, timeout: int = 1800) -> None:
    nb_path = BASE / nb_rel_path
    cmd = [
        'jupyter', 'nbconvert', '--to', 'notebook', '--execute', str(nb_path),
        '--output', Path(nb_rel_path).name.replace('.ipynb', f'.{run_dir.name}.executed.ipynb'),
        '--ExecutePreprocessor.timeout', str(timeout),
        '--ExecutePreprocessor.kernel_name', 'python3'
    ]
    print(f'Executing {nb_rel_path} in {run_dir}...')
    res = subprocess.run(cmd, cwd=str(run_dir))
    if res.returncode != 0:
        raise SystemExit(f'Notebook failed: {nb_rel_path} (region={run_dir.name})')


def copy_reports_from_run(run_dir: Path, target_subdir: str) -> Path:
    src = run_dir / 'reports' / '2025-08-15'
    dst = REPORTS_BASE / target_subdir
    if not src.exists():
        print('WARN: run reports not found:', src)
        dst.mkdir(parents=True, exist_ok=True)
        return dst
    if dst.exists():
        shutil.rmtree(dst)
    shutil.copytree(src, dst)
    print('Copied run reports to', dst)
    return dst


def generate_comparative_summary(sp_dir: Path, rest_dir: Path):
    out_dir = REPORTS_BASE / 'comparisons'
    out_dir.mkdir(parents=True, exist_ok=True)

    # Load algorithm ranking from both
    def load_csv(base: Path, rel: str) -> pd.DataFrame | None:
        p = base / rel
        return pd.read_csv(p) if p.exists() else None

    sp_rank = load_csv(sp_dir, 'tables/algorithm_ranking.csv')
    rb_rank = load_csv(rest_dir, 'tables/algorithm_ranking.csv')

    rows = []
    if sp_rank is not None:
        for _, r in sp_rank.iterrows():
            rows.append({'region': 'Sao Paulo', 'model': r['model'],
                         'rmse': r.get('rmse_mean', np.nan), 'mae': r.get('mae_mean', np.nan), 'r2': r.get('r2_mean', np.nan)})
    if rb_rank is not None:
        for _, r in rb_rank.iterrows():
            rows.append({'region': 'Rest Brazil', 'model': r['model'],
                         'rmse': r.get('rmse_mean', np.nan), 'mae': r.get('mae_mean', np.nan), 'r2': r.get('r2_mean', np.nan)})
    perf_df = pd.DataFrame(rows)
    perf_path = out_dir / 'regional_model_performance.csv'
    perf_df.to_csv(perf_path, index=False)

    # Plot side-by-side bars for R2
    if not perf_df.empty:
        plt.figure(figsize=(10,6))
        sns.barplot(data=perf_df, x='model', y='r2', hue='region')
        plt.title('R² por Modelo e Região (SP vs Resto)')
        plt.xlabel('Modelo')
        plt.ylabel('R² (médio CV)')
        plt.legend(title='Região')
        plt.tight_layout()
        plt.savefig(out_dir / 'r2_by_region.png', bbox_inches='tight', dpi=300)
        plt.close()

    # Territorial recommendations comparison
    sp_ter = load_csv(sp_dir, 'territorial_analysis/ranking_supervisionado.csv')
    rb_ter = load_csv(rest_dir, 'territorial_analysis/ranking_supervisionado.csv')
    if sp_ter is not None and rb_ter is not None:
        key = [c for c in sp_ter.columns if c not in {'receita_real','receita_pred','score_supervisionado','rank_supervisionado'}]
        key = key[0] if key else sp_ter.columns[0]
        sp_top = sp_ter[[key, 'rank_supervisionado', 'score_supervisionado']].copy(); sp_top['region']='Sao Paulo'
        rb_top = rb_ter[[key, 'rank_supervisionado', 'score_supervisionado']].copy(); rb_top['region']='Rest Brazil'
        comp = pd.concat([sp_top, rb_top], ignore_index=True)
        comp.to_csv(out_dir / 'territorial_recommendations_comparison.csv', index=False)

    print('Comparative summaries saved in', out_dir)


def main():
    print('Loading dataset:', DATA_PATH)
    df = pd.read_csv(DATA_PATH, low_memory=False)
    n0 = len(df)

    # Determine SP mask
    sp_mask = filter_sp(df)
    df_sp = df[sp_mask].copy()
    df_rest = df[~sp_mask].copy()

    # QA checks
    print('Samples -> total:', n0, '| SP:', len(df_sp), '| Rest:', len(df_rest))
    if len(df_sp) + len(df_rest) != n0:
        print('Warning: split sizes do not match original total!')
    # Persist sample stats
    stats = pd.DataFrame([
        {'region':'Sao Paulo','n':len(df_sp),'mean_val': float(df_sp['valor'].mean()) if 'valor' in df_sp.columns else np.nan},
        {'region':'Rest Brazil','n':len(df_rest),'mean_val': float(df_rest['valor'].mean()) if 'valor' in df_rest.columns else np.nan},
    ])
    (REPORTS_BASE/'comparisons').mkdir(parents=True, exist_ok=True)
    stats.to_csv(REPORTS_BASE/'comparisons'/'regional_sample_stats.csv', index=False)

    # Prepare runs
    run_sp = prepare_region_run(df_sp, 'sao_paulo')
    run_rb = prepare_region_run(df_rest, 'rest_brazil')

    # Execute pipeline notebooks for each region
    notebooks = [
        'notebooks/01_data_exploration_analysis.ipynb',
        'notebooks/02_feature_engineering.ipynb',
        'notebooks/03_model_development_comparison.ipynb',
        'notebooks/04_territorial_analysis.ipynb',
    ]

    # Execute for SP and REST; copy artifacts from each run_dir to project reports
    for run_dir, tag in [(run_sp, 'sao_paulo'), (run_rb, 'rest_brazil')]:
        for nb in notebooks:
            run_notebook(nb, run_dir)
        copy_reports_from_run(run_dir, tag)

    sp_reports = REPORTS_BASE / 'sao_paulo'
    rb_reports = REPORTS_BASE / 'rest_brazil'

    # Generate comparative summary
    generate_comparative_summary(sp_reports, rb_reports)

    print('Regional analysis complete.')

if __name__ == '__main__':
    main()

