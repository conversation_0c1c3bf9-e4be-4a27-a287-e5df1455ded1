from pathlib import Path
import sys
import numpy as np
import pandas as pd
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_absolute_error, r2_score

BASE = Path(__file__).resolve().parents[1]
sys.path.append(str(BASE))

ORIG = BASE/'data'/'processed'/'features_engineered_regional.csv'
CLEAN = BASE/'data'/'processed'/'features_cleaned_pre_territorial.csv'
TIME_SAFE = BASE/'data'/'processed'/'features_cleaned_pre_territorial_time.csv'
REPORTS = BASE/'reports'/'2025-08-15'
TABLES = REPORTS/'tables'
DOCS = REPORTS/'docs'
TABLES.mkdir(parents=True, exist_ok=True)
DOCS.mkdir(parents=True, exist_ok=True)

TARGET = 'valor'
GROUP_KEY = '__orig_code_by_rank__' if '__orig_code_by_rank__' in pd.read_csv(ORIG, nrows=0).columns else 'Dim_Lojas.Cod_Franqueado'


def prepare(df: pd.DataFrame, allowed_cols=None) -> tuple[pd.DataFrame, np.ndarray, list[str]]:
    if TARGET not in df.columns:
        raise SystemExit(f'Target {TARGET} not found')
    y = pd.to_numeric(df[TARGET], errors='coerce').fillna(0.0).values
    # remove leakage target-derived
    ban = {TARGET}
    Xdf = df.drop(columns=[c for c in df.columns if c in ban])
    # numeric only
    Xnum = Xdf.select_dtypes(include=[np.number]).copy().fillna(0.0)
    # optional allowlist
    if allowed_cols is not None:
        keep = [c for c in Xnum.columns if c in allowed_cols]
        Xnum = Xnum[keep]
    return Xnum, y, Xnum.columns.tolist()


def time_cv_eval(df: pd.DataFrame, feature_allow=None, label='orig') -> dict:
    # temporal order proxy: if 'data' exists use it; else use store_age_days/month/day as proxy order
    if 'data' in df.columns:
        order = pd.to_datetime(df['data'], errors='coerce')
    elif 'store_age_days' in df.columns:
        order = df['store_age_days']
    elif 'month' in df.columns and 'day' in df.columns:
        order = (df['month']*31 + df['day'])
    else:
        order = np.arange(len(df))

    X, y, cols = prepare(df, allowed_cols=feature_allow)
    ord_idx = np.argsort(order.values if hasattr(order, 'values') else order)
    X = X.iloc[ord_idx]
    y = y[ord_idx]

    tscv = TimeSeriesSplit(n_splits=5)
    from sklearn.linear_model import Ridge
    model = Ridge(alpha=1.0, random_state=42)

    maes = []; rmses = []; mapes = []; r2s = []
    imps = np.zeros(X.shape[1])

    for train_idx, test_idx in tscv.split(X):
        Xtr, Xte = X.iloc[train_idx], X.iloc[test_idx]
        ytr, yte = y[train_idx], y[test_idx]
        model.fit(Xtr, ytr)
        p = model.predict(Xte)
        mae = mean_absolute_error(yte, p)
        rmse = np.sqrt(((yte - p)**2).mean())
        mape = (np.abs((yte - p) / np.clip(np.abs(yte), 1e-6, None))).mean()
        r2 = r2_score(yte, p)
        maes.append(mae); rmses.append(rmse); mapes.append(mape); r2s.append(r2)
        if hasattr(model, 'coef_'):
            imps += np.abs(model.coef_)

    res = {
        'label': label,
        'MAE': float(np.mean(maes)),
        'RMSE': float(np.mean(rmses)),
        'MAPE': float(np.mean(mapes)),
        'R2': float(np.mean(r2s)),
        'features': X.columns.tolist(),
        'importances': imps.tolist(),
    }
    return res


def main():
    df_orig = pd.read_csv(ORIG, low_memory=False)
    df_clean = pd.read_csv(CLEAN, low_memory=False)
    df_time = pd.read_csv(TIME_SAFE, low_memory=False)

    # Ensure same split order proxy (fallbacks above), evaluate with simple Ridge for signal
    res_orig = time_cv_eval(df_orig, label='original')
    res_clean = time_cv_eval(df_clean, label='cleaned')
    res_time = time_cv_eval(df_time, label='time_safe')

    # Evaluate enriched datasets if available
    comp_rows = [
        {'dataset':'original', **{k:v for k,v in res_orig.items() if k in {'MAE','RMSE','MAPE','R2'}}},
        {'dataset':'cleaned', **{k:v for k,v in res_clean.items() if k in {'MAE','RMSE','MAPE','R2'}}},
        {'dataset':'time_safe', **{k:v for k,v in res_time.items() if k in {'MAE','RMSE','MAPE','R2'}}},
    ]
    enriched_uf = BASE/'data'/'processed'/'features_enriched_uf.csv'
    if enriched_uf.exists():
        df_uf = pd.read_csv(enriched_uf, low_memory=False)
        res_uf = time_cv_eval(df_uf, label='enriched_uf')
        comp_rows.append({'dataset':'enriched_uf', **{k:v for k,v in res_uf.items() if k in {'MAE','RMSE','MAPE','R2'}}})
    enriched_m = BASE/'data'/'processed'/'features_enriched_municipio.csv'
    if enriched_m.exists():
        df_m = pd.read_csv(enriched_m, low_memory=False)
        res_m = time_cv_eval(df_m, label='enriched_municipio')
        comp_rows.append({'dataset':'enriched_municipio', **{k:v for k,v in res_m.items() if k in {'MAE','RMSE','MAPE','R2'}}})

    comp = pd.DataFrame(comp_rows)
    comp_path = TABLES/'validation_comparison.csv'
    comp.to_csv(comp_path, index=False)

    # Save top importances (feature names with highest absolute coefs)
    def topk(res, k=20):
        return pd.DataFrame({'feature': res['features'], 'importance': res['importances']}).sort_values('importance', ascending=False).head(k)
    top_orig = topk(res_orig)
    top_clean = topk(res_clean)
    top_time = topk(res_time)
    top_orig.to_csv(TABLES/'top_importances_original.csv', index=False)
    top_clean.to_csv(TABLES/'top_importances_cleaned.csv', index=False)
    top_time.to_csv(TABLES/'top_importances_time_safe.csv', index=False)

    # Markdown summary
    lines = []
    lines.append('# Validação pós-limpeza (CV temporal)')
    lines.append('')
    lines.append('## Métricas médias (Ridge-like, 5 folds temporais)')
    lines.append(comp.to_csv(index=False))
    lines.append('')
    lines.append('## Observações')
    lines.append('- Resultados com modelo simples indicam direção do impacto; usar modelos finais para confirmação.')
    lines.append('- Conjunto time_safe inclui store_avg_valor_t1 sem leakage.')
    DOCS.joinpath('validation_post_cleaning.md').write_text('\n'.join(lines), encoding='utf-8')

    print('Saved comparison to', comp_path)


if __name__ == '__main__':
    main()

