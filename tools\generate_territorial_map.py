from pathlib import Path
import pandas as pd
import numpy as np
import json
import requests
import plotly.express as px

BASE = Path('.')
REPORTS = BASE/'reports'/'2025-08-15'
TABLES = REPORTS/'tables'
PLOTS = REPORTS/'plots'
TABLES.mkdir(parents=True, exist_ok=True)
PLOTS.mkdir(parents=True, exist_ok=True)

# Try to load clustering assignments
assign_path = TABLES/'cluster_assignments.csv'
DATA = BASE/'data'/'processed'/'features_engineered_regional.csv'
df = pd.read_csv(DATA, low_memory=False)
if assign_path.exists():
    cl = pd.read_csv(assign_path)
    if 'cluster' in cl.columns:
        labels = cl['cluster'].values
        if len(labels) < len(df):
            labels = np.pad(labels, (0, len(df)-len(labels)), constant_values=labels[-1])
    else:
        labels = np.zeros(len(df), dtype=int)
else:
    # fallback: compute proxy clusters using territorial signals
    from sklearn.preprocessing import OneHotEncoder
    from sklearn.compose import ColumnTransformer
    from sklearn.cluster import KMeans
    territorial_cols = []
    if 'UF' in df.columns:
        territorial_cols.append('UF')
    territorial_cols += [c for c in df.columns if 'REGIAO_CHILLI' in c]
    if territorial_cols:
        X_terr = df[territorial_cols].copy()
        transformers=[]
        if 'UF' in X_terr.columns:
            transformers.append(('uf', OneHotEncoder(handle_unknown='ignore'), ['UF']))
        fixed = [c for c in X_terr.columns if c!='UF']
        if fixed:
            transformers.append(('pass', 'passthrough', fixed))
        pre = ColumnTransformer(transformers)
        X_enc = pre.fit_transform(X_terr)
        km = KMeans(n_clusters=5, random_state=42, n_init=10)
        labels = km.fit_predict(X_enc)
    else:
        labels = np.zeros(len(df), dtype=int)

# Build dominant cluster per UF
if 'UF' in df.columns:
    uf_series = df['UF'].astype(str)
else:
    oh_uf = [c for c in df.columns if c.startswith('UF_')]
    if oh_uf:
        idx = np.argmax(df[oh_uf].values, axis=1)
        uf_series = pd.Series([oh_uf[i].replace('UF_','') for i in idx])
    else:
        uf_series = None

if uf_series is None:
    print('UF not available; cannot draw choropleth')
    raise SystemExit(0)

dom = (
    pd.DataFrame({'UF': uf_series, 'cluster': labels})
      .groupby('UF')['cluster'].agg(lambda s: s.value_counts().idxmax()).reset_index()
)
sizes = uf_series.value_counts().rename('count').reset_index().rename(columns={'index':'UF'})
dom = dom.merge(sizes, on='UF', how='left')

# GeoJSON
urls = [
    'https://raw.githubusercontent.com/codeforamerica/click_that_hood/master/public/data/brazil-states.geojson',
]
geojson = None
for u in urls:
    try:
        geojson = requests.get(u, timeout=20).json()
        break
    except Exception:
        continue

fig = px.choropleth(
    dom, geojson=geojson, locations='UF', color='cluster', featureidkey='properties.sigla' if geojson else None,
    color_continuous_scale=px.colors.qualitative.Set1 if hasattr(px.colors, 'qualitative') else 'Viridis',
    hover_data={'UF':True, 'cluster':True, 'count':True},
)
fig.update_geos(fitbounds="locations", visible=False)
fig.update_layout(margin=dict(l=0,r=0,t=30,b=0), title='Clusters territoriais por UF (dominante)')

png = PLOTS/'territorial_clustering_map.png'
html = PLOTS/'territorial_clustering_map.html'
try:
    fig.write_image(str(png), scale=2)
    print('Saved PNG to', png)
except Exception as e:
    print('PNG export failed:', e)
    fig.write_html(str(html))
    print('Saved HTML to', html)

