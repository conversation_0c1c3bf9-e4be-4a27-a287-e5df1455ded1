{"processed": [{"notebook": "notebooks\\chilli_beans_analysis.ipynb", "changes": [{"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 105}], "detections": [{"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 1, "pattern": "id_filter_usage"}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 1, "pattern": "id_filter_usage"}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 2, "pattern": "id_filter_usage"}, {"notebook": "notebooks\\chilli_beans_analysis.ipynb", "cell": 2, "pattern": "id_filter_usage"}]}, {"notebook": "notebooks\\model_comparison_colab.ipynb", "changes": [], "detections": [{"notebook": "notebooks\\model_comparison_colab.ipynb", "cell": 1, "pattern": "id_filter_usage"}, {"notebook": "notebooks\\model_comparison_colab.ipynb", "cell": 1, "pattern": "id_filter_usage"}, {"notebook": "notebooks\\model_comparison_colab.ipynb", "cell": 2, "pattern": "id_filter_usage"}, {"notebook": "notebooks\\model_comparison_colab.ipynb", "cell": 2, "pattern": "id_filter_usage"}]}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "changes": [{"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 105}], "detections": [{"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 1, "pattern": "id_filter_usage"}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 1, "pattern": "id_filter_usage"}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 2, "pattern": "id_filter_usage"}, {"notebook": "notebooks\\preprocessamento\\eda_distribuicoes.ipynb", "cell": 2, "pattern": "id_filter_usage"}]}, {"notebook": "notebooks\\preprocessamento\\eda_geografico.ipynb", "changes": [], "detections": [{"notebook": "notebooks\\preprocessamento\\eda_geografico.ipynb", "cell": 1, "pattern": "id_filter_usage"}, {"notebook": "notebooks\\preprocessamento\\eda_geografico.ipynb", "cell": 1, "pattern": "id_filter_usage"}, {"notebook": "notebooks\\preprocessamento\\eda_geografico.ipynb", "cell": 2, "pattern": "id_filter_usage"}, {"notebook": "notebooks\\preprocessamento\\eda_geografico.ipynb", "cell": 2, "pattern": "id_filter_usage"}]}], "total_changes": 2, "total_detections": 16}