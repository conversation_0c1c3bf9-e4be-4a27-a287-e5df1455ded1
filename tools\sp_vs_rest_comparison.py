from pathlib import Path
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error

from feature_filter import compute_clean_features
from cluster_business_naming import make_cluster_business_names

BASE = Path('.')
REPORTS = BASE/'reports'/'2025-08-15'
TABLES = REPORTS/'tables'
PLOTS = REPORTS/'plots'
PRES = PLOTS/'presentation'
TABLES.mkdir(parents=True, exist_ok=True)
PLOTS.mkdir(parents=True, exist_ok=True)
PRES.mkdir(parents=True, exist_ok=True)

DATA = BASE/'data'/'processed'/'features_engineered_regional.csv'
df_full = pd.read_csv(DATA, low_memory=False)

TARGET = 'valor' if 'valor' in df_full.columns else (df_full.select_dtypes('number').columns[-1])

# Load cluster labels (full) and business names
assign_path = TABLES/'cluster_assignments.csv'
if assign_path.exists():
    cl = pd.read_csv(assign_path)
    labels_full = cl['cluster'].values if 'cluster' in cl.columns else np.zeros(len(df_full), dtype=int)
    if len(labels_full) < len(df_full):
        labels_full = np.pad(labels_full, (0, len(df_full)-len(labels_full)), constant_values=labels_full[-1])
else:
    reg = [c for c in df_full.columns if 'REGIAO_CHILLI' in c]
    labels_full = np.argmax(df_full[reg].values, axis=1) if reg else np.zeros(len(df_full), dtype=int)

names_df = make_cluster_business_names(df_full, labels_full, REPORTS)
name_map = {int(r.cluster): r.business_name for _, r in names_df.iterrows()}


def mk_estimator():
    base = RandomForestRegressor(n_estimators=160, random_state=42, n_jobs=-1)
    return Pipeline([('sc', StandardScaler(with_mean=False)), ('model', base)])


def evaluate_scenario(df_scn: pd.DataFrame, labels_scn: np.ndarray, scenario: str):
    clusters = sorted(pd.Series(labels_scn).dropna().unique())
    rows = []
    preds = []
    feat_imps = {}
    debug_rows = []
    for c_id in clusters:
        mask = (pd.Series(labels_scn)==c_id).values
        dfc = df_scn.loc[mask].copy()
        if len(dfc) < 120:
            continue
        # optional downsample for speed, before split (keeps anti-leak correctness)
        if len(dfc) > 8000:
            idx_ds = np.random.RandomState(42).choice(len(dfc), 8000, replace=False)
            dfc = dfc.iloc[idx_ds]
        # split into train/test indices
        idx_all = np.arange(len(dfc))
        idx_tr, idx_te = train_test_split(idx_all, test_size=0.2, random_state=42)
        df_tr = dfc.iloc[idx_tr]
        df_te = dfc.iloc[idx_te]
        # compute clean features ONLY on training to avoid leakage
        # stricter suspicious patterns and correlation threshold for safety
        clean_cols, audit_removed = compute_clean_features(
            df_tr, TARGET, REPORTS, corr_threshold=0.95,
            suspicious_additional=['sp_', 'uf_', 'estado_', 'regiao_', 'regional_', 'loja_', 'franquia_']
        )
        # build X/y from respective splits
        Xtr = df_tr[clean_cols].copy(); ytr = pd.to_numeric(df_tr[TARGET], errors='coerce').fillna(0.0)
        Xte = df_te[clean_cols].copy(); yte = pd.to_numeric(df_te[TARGET], errors='coerce').fillna(0.0)
        # quick diagnostics
        # any feature equals target in train?
        try:
            eq_target = bool(any([Xtr[c].equals(ytr) for c in clean_cols]))
        except Exception:
            eq_target = False
        # correlations on train
        with np.errstate(all='ignore'):
            corr_train = Xtr.corrwith(ytr).abs().sort_values(ascending=False)
        high_corr_cnt = int((corr_train > 0.98).sum())
        debug_rows.append({'scenario':scenario,'cluster':int(c_id),'n_total':int(len(dfc)),'n_train':int(len(df_tr)),'n_test':int(len(df_te)),'clean_cols':int(len(clean_cols)),'high_corr_gt_098_train':high_corr_cnt,'any_feature_equals_target':eq_target})

        est = mk_estimator()
        est.fit(Xtr, ytr)
        yp = est.predict(Xte)
        r2 = float(r2_score(yte, yp)); rmse = float(np.sqrt(mean_squared_error(yte, yp))); mae = float(mean_absolute_error(yte, yp))
        # bootstrap
        rng = np.random.RandomState(42); n=len(yte); B=80
        r2s=[]; rmses=[]; maes=[]
        for _ in range(B):
            idx = rng.choice(n, n, replace=True)
            yt = np.array(yte)[idx]; yp_b = np.array(yp)[idx]
            with np.errstate(all='ignore'):
                r2s.append(r2_score(yt, yp_b))
            rmses.append(float(np.sqrt(mean_squared_error(yt, yp_b))))
            maes.append(float(mean_absolute_error(yt, yp_b)))
        def ci(a):
            lo, hi = np.percentile(a, [2.5, 97.5]); return float(lo), float(hi)
        r2_lo,r2_hi=ci(np.asarray(r2s)); rmse_lo,rmse_hi=ci(np.asarray(rmses)); mae_lo,mae_hi=ci(np.asarray(maes))
        rows.append({'scenario':scenario,'cluster':int(c_id),'cluster_name':name_map.get(int(c_id), f'Cluster {int(c_id)}'),
                     'n':int(len(dfc)),'r2':r2,'r2_lo':r2_lo,'r2_hi':r2_hi,'rmse':rmse,'rmse_lo':rmse_lo,'rmse_hi':rmse_hi,'mae':mae,'mae_lo':mae_lo,'mae_hi':mae_hi,
                     'r2_se':float(np.std(r2s, ddof=1)), 'rmse_se':float(np.std(rmses, ddof=1)), 'mae_se':float(np.std(maes, ddof=1))})
        preds.append(pd.DataFrame({'scenario':scenario,'cluster':int(c_id),'y_true':yte.values,'y_pred':yp}))
        # feature importances
        try:
            imp = est.named_steps['model'].feature_importances_
            feat_imps[int(c_id)] = pd.Series(imp, index=clean_cols).sort_values(ascending=False)
        except Exception:
            pass
    mdf = pd.DataFrame(rows)
    pdf = pd.concat(preds, ignore_index=True) if preds else pd.DataFrame()
    dbg = pd.DataFrame(debug_rows)
    dbg.to_csv(TABLES/f'sp_debug_scenario_{scenario}.csv', index=False)
    return mdf, pdf, feat_imps


# Scenario A: Full dataset
mdf_A, pdf_A, imps_A = evaluate_scenario(df_full, labels_full, 'A')

# Scenario B: Without São Paulo
if 'UF' in df_full.columns:
    mask_B = df_full['UF'] != 'SP'
else:
    mask_B = ~df_full.get('UF_SP', pd.Series(False, index=df_full.index)).astype(bool)

df_B = df_full.loc[mask_B].reset_index(drop=True)
labels_B = pd.Series(labels_full).loc[mask_B].values
mdf_B, pdf_B, imps_B = evaluate_scenario(df_B, labels_B, 'B')

# High-level diagnostics per scenario
hl = []
hl.append({'scenario':'A','rows':int(len(df_full)),'ufs':int(df_full['UF'].nunique() if 'UF' in df_full.columns else 0),'sp_rows':int((df_full['UF']=='SP').sum() if 'UF' in df_full.columns else int(df_full.get('UF_SP', pd.Series(False,index=df_full.index)).sum()) )})
hl.append({'scenario':'B','rows':int(len(df_B)),'ufs':int(df_B['UF'].nunique() if 'UF' in df_B.columns else 0),'sp_rows':int((df_B['UF']=='SP').sum() if 'UF' in df_B.columns else int(df_B.get('UF_SP', pd.Series(False,index=df_B.index)).sum()) )})
pd.DataFrame(hl).to_csv(TABLES/'sp_comparison_highlevel.csv', index=False)

# Save per-scenario metrics and preds
all_metrics = pd.concat([mdf_A, mdf_B], ignore_index=True)
all_metrics.to_csv(TABLES/'sp_comparison_metrics_by_cluster.csv', index=False)
all_preds = pd.concat([pdf_A, pdf_B], ignore_index=True)
all_preds.to_csv(TABLES/'sp_comparison_predictions.csv', index=False)

# Differences with significance approx (normal on bootstrap SE)
A = mdf_A.set_index('cluster'); B = mdf_B.set_index('cluster')
clust = sorted(set(A.index).intersection(B.index))
rows=[]
for c in clust:
    def diff_ci(a, b, sa, sb):
        d = b - a
        se = np.sqrt(sa**2 + sb**2)
        lo = d - 1.96*se; hi = d + 1.96*se
        # two-sided p-value approx
        from math import erf, sqrt
        z = 0 if se==0 else d/se
        # approx p using normal CDF
        p = 2*(1-0.5*(1+erf(abs(z)/np.sqrt(2))))
        return d, lo, hi, float(p)
    r2_d, r2_lo, r2_hi, r2_p = diff_ci(A.loc[c,'r2'], B.loc[c,'r2'], A.loc[c,'r2_se'], B.loc[c,'r2_se'])
    rmse_d, rmse_lo, rmse_hi, rmse_p = diff_ci(A.loc[c,'rmse'], B.loc[c,'rmse'], A.loc[c,'rmse_se'], B.loc[c,'rmse_se'])
    mae_d, mae_lo, mae_hi, mae_p = diff_ci(A.loc[c,'mae'], B.loc[c,'mae'], A.loc[c,'mae_se'], B.loc[c,'mae_se'])
    rows.append({'cluster':int(c),'cluster_name':name_map.get(int(c), f'Cluster {int(c)}'),
                 'delta_r2':r2_d,'delta_r2_lo':r2_lo,'delta_r2_hi':r2_hi,'p_r2':r2_p,
                 'delta_rmse':rmse_d,'delta_rmse_lo':rmse_lo,'delta_rmse_hi':rmse_hi,'p_rmse':rmse_p,
                 'delta_mae':mae_d,'delta_mae_lo':mae_lo,'delta_mae_hi':mae_hi,'p_mae':mae_p,
                 'n_A':int(A.loc[c,'n']),'n_B':int(B.loc[c,'n'])})

diffs = pd.DataFrame(rows).sort_values('cluster')
diffs.to_csv(TABLES/'sp_comparison_diffs_by_cluster.csv', index=False)

# Cluster distribution differences
dist_A = pd.Series(labels_full).value_counts().sort_index().rename('A')
dist_B = pd.Series(labels_B).value_counts().sort_index().rename('B')
dist = pd.concat([dist_A, dist_B], axis=1).fillna(0).astype(int)
dist.index.name = 'cluster'
dist['cluster_name'] = [name_map.get(int(c), f'Cluster {int(c)}') for c in dist.index]
dist.to_csv(TABLES/'sp_comparison_cluster_distribution.csv', index=True)

# ----------------- Visualizações -----------------
sns.set_style('whitegrid')

# 1) Distribuição de clusters (contagem e participação)
plt.figure(figsize=(12,5))
ax1 = plt.subplot(1,2,1)
ax1.bar(dist['cluster_name'], dist['A'], alpha=0.8, label='Com SP')
ax1.bar(dist['cluster_name'], dist['B'], alpha=0.8, label='Sem SP')
ax1.set_title('Distribuição de Clusters — Contagem (A: com SP vs B: sem SP)')
ax1.set_xticklabels(dist['cluster_name'], rotation=30, ha='right')
ax1.set_ylabel('Quantidade de registros')
ax1.legend()
ax2 = plt.subplot(1,2,2)
share = dist[['A','B']].div(dist[['A','B']].sum(axis=0), axis=1)
ax2.plot(dist['cluster_name'], share['A'], marker='o', label='Com SP')
ax2.plot(dist['cluster_name'], share['B'], marker='o', label='Sem SP')
ax2.set_title('Distribuição de Clusters — Participação relativa')
ax2.set_xticklabels(dist['cluster_name'], rotation=30, ha='right')
ax2.set_ylabel('Participação')
ax2.legend()
plt.suptitle('SP vs Resto do Brasil — Impacto na Distribuição de Clusters\nFonte: features_engineered_regional.csv; Clusters conforme atribuições atuais', fontsize=10)
plt.tight_layout(rect=[0,0,1,0.92])
plt.savefig(PRES/'sp_vs_rest_cluster_distribution.png', dpi=220)
plt.close()

# 2) Métricas lado a lado (R2/RMSE/MAE) com IC
for metric, title, ylabel in [
    ('r2','R² por Cluster (95% CI)','R²'),
    ('rmse','RMSE por Cluster (95% CI)','RMSE'),
    ('mae','MAE por Cluster (95% CI)','MAE')
]:
    g = all_metrics.pivot_table(index='cluster_name', columns='scenario', values=[metric, metric+'_lo', metric+'_hi'])
    idx = list(g.index)
    x = np.arange(len(idx))
    width = 0.35
    fig, ax = plt.subplots(figsize=(14,5))
    for i, sc in enumerate(['A','B']):
        m = g[(metric, sc)].values.astype(float)
        lo = g[(metric+'_lo', sc)].values.astype(float)
        hi = g[(metric+'_hi', sc)].values.astype(float)
        err = np.vstack([m-lo, hi-m])
        ax.bar(x + i*width - width/2, m, width=width, yerr=err, capsize=4, label=('Com SP' if sc=='A' else 'Sem SP'))
    ax.set_xticks(x); ax.set_xticklabels(idx, rotation=30, ha='right')
    ax.set_title(f'{title} — A (com SP) vs B (sem SP)')
    ax.set_ylabel(ylabel)
    ax.legend()
    plt.tight_layout()
    plt.savefig(PRES/f'sp_vs_rest_metrics_{metric}.png', dpi=220)
    plt.close()

# 3) Significância estatística (diferença B-A)
if not diffs.empty:
    fig, ax = plt.subplots(figsize=(12,6))
    ax.hlines(0, -0.5, len(diffs)-0.5, colors='gray', linestyles='dashed', linewidth=1)
    x = np.arange(len(diffs))
    y = diffs['delta_r2'].values
    lo = diffs['delta_r2_lo'].values
    hi = diffs['delta_r2_hi'].values
    ax.errorbar(x, y, yerr=[y-lo, hi-y], fmt='o', capsize=4)
    sig = diffs['p_r2'] < 0.05
    for i, s in enumerate(sig):
        if s:
            ax.text(x[i], y[i], '*', color='red', fontsize=14, ha='center', va='bottom')
    ax.set_xticks(x); ax.set_xticklabels(diffs['cluster_name'], rotation=30, ha='right')
    ax.set_title('Diferença de R² (B - A) por Cluster — IC ~95% e indicação de significância (p<0,05)')
    ax.set_ylabel('Δ R² (Sem SP - Com SP)')
    plt.tight_layout()
    plt.savefig(PRES/'sp_vs_rest_significance.png', dpi=220)
    plt.close()

# 4) Importâncias de features — mudanças com exclusão de SP
# Seleciona top 3 clusters por n em A
top3 = mdf_A.sort_values('n', ascending=False)['cluster'].head(3).tolist()
for c in top3:
    impA = imps_A.get(int(c)); impB = imps_B.get(int(c))
    if impA is None or impB is None:
        continue
    top_feats = list(pd.concat([impA.head(10), impB.head(10)]).sort_values(ascending=False).head(12).index)
    vA = impA.reindex(top_feats).fillna(0)
    vB = impB.reindex(top_feats).fillna(0)
    fig, axes = plt.subplots(1,2, figsize=(14,6), sharey=True)
    axes[0].barh(top_feats, vA.values, color='#1f77b4'); axes[0].invert_yaxis(); axes[0].set_title(f'Com SP — {name_map.get(int(c))}')
    axes[1].barh(top_feats, vB.values, color='#ff7f0e'); axes[1].set_title(f'Sem SP — {name_map.get(int(c))}')
    fig.suptitle(f'Importância de Features (RF) — Cluster {name_map.get(int(c))}\nMudanças ao excluir SP (comparativo A vs B)')
    plt.tight_layout(rect=[0,0,1,0.92])
    plt.savefig(PRES/f'sp_vs_rest_feature_importance_cluster_{int(c)}.png', dpi=220)
    plt.close()

# 5) Impacto do tamanho de amostra (Δn vs ΔR²)
try:
    if not diffs.empty:
        diffs['delta_n'] = diffs['n_B'] - diffs['n_A']
        fig, ax = plt.subplots(figsize=(8,6))
        ax.scatter(diffs['delta_n'], diffs['delta_r2'], c=(diffs['p_r2']<0.05).map({True:'red', False:'gray'}))
        for _, r in diffs.iterrows():
            ax.text(r['delta_n'], r['delta_r2'], r['cluster_name'], fontsize=8, ha='left', va='bottom')
        ax.axhline(0, color='k', lw=1, ls='--'); ax.axvline(0, color='k', lw=1, ls=':')
        ax.set_xlabel('Δ Amostra (Sem SP - Com SP)'); ax.set_ylabel('Δ R² (Sem SP - Com SP)')
        ax.set_title('Impacto do Tamanho de Amostra em R² — SP vs Resto\nMarcadores em vermelho: diferença significativa (p<0,05)')
        plt.tight_layout(); plt.savefig(PRES/'sp_vs_rest_sample_impact.png', dpi=220); plt.close()

        # Resumo quantitativo
        sig_pos = int(((diffs['p_r2']<0.05) & (diffs['delta_r2']>0)).sum())
        sig_neg = int(((diffs['p_r2']<0.05) & (diffs['delta_r2']<0)).sum())
        neutral = int(((diffs['p_r2']>=0.05)).sum())
        summary = pd.DataFrame({'metric':['r2'], 'sig_improve':[sig_pos], 'sig_worse':[sig_neg], 'not_significant':[neutral]})
        summary.to_csv(TABLES/'sp_comparison_summary.csv', index=False)
except Exception as e:
    print('Sample impact plot failed:', e)

# 6) Predito vs Real e diagnósticos de regressão por cluster (top 4 por n em A)
try:
    top4 = mdf_A.sort_values('n', ascending=False)['cluster'].head(4).tolist()
    combined = pd.concat([pdf_A, pdf_B], ignore_index=True)
    for c in top4:
        sub = combined[combined['cluster']==int(c)].copy()
        if sub.empty: continue
        # scatter Predito vs Real, cor por cenário
        plt.figure(figsize=(6,6))
        sns.scatterplot(data=sub, x='y_true', y='y_pred', hue='scenario', palette={'A':'#1f77b4','B':'#ff7f0e'}, s=14, alpha=0.6)
        lim = (min(sub['y_true'].min(), sub['y_pred'].min()), max(sub['y_true'].max(), sub['y_pred'].max()))
        plt.plot(lim, lim, 'k--', lw=1); plt.xlabel('Real'); plt.ylabel('Previsto')
        plt.title(f'Predito vs Real — {name_map.get(int(c))} (A: com SP vs B: sem SP)')
        plt.tight_layout(); plt.savefig(PRES/f'sp_vs_rest_pred_real_cluster_{int(c)}.png', dpi=220); plt.close()

        # resíduos vs ajustado
        sub['resid'] = sub['y_pred'] - sub['y_true']
        fig, axes = plt.subplots(1,2, figsize=(12,5), sharey=True)
        for i, sc in enumerate(['A','B']):
            s = sub[sub['scenario']==sc]
            axes[i].scatter(s['y_pred'], s['resid'], s=10, alpha=0.5)
            axes[i].axhline(0, color='k', lw=1, ls='--')
            axes[i].set_title('Resíduos vs Previsto — ' + ('Com SP' if sc=='A' else 'Sem SP'))
            axes[i].set_xlabel('Previsto'); axes[i].set_ylabel('Resíduo')
        fig.suptitle(f'Diagnóstico de Resíduos — {name_map.get(int(c))}')
        plt.tight_layout(rect=[0,0,1,0.92])
        plt.savefig(PRES / f'sp_vs_rest_residuals_cluster_{int(c)}.png', dpi=220)
        plt.close()

        # distribuição Real vs Previsto
        plt.figure(figsize=(7,5))
        for sc, col in [('A','#1f77b4'),('B','#ff7f0e')]:
            s = sub[sub['scenario']==sc]
            if len(s)>5:
                sns.kdeplot(s['y_true'], color=col, linestyle='--', label=('Real '+('c/ SP' if sc=='A' else 's/ SP')))
                sns.kdeplot(s['y_pred'], color=col, linestyle='-', label=('Previsto '+('c/ SP' if sc=='A' else 's/ SP')))
        plt.title(f'Distribuições — {name_map.get(int(c))}'); plt.xlabel('Valor'); plt.ylabel('Densidade'); plt.legend()
        plt.tight_layout(); plt.savefig(PRES/f'sp_vs_rest_distribution_cluster_{int(c)}.png', dpi=220); plt.close()
except Exception as e:
    print('Diagnostics generation failed:', e)

# 7) KPI cards e resumo executivo
try:
    # Weighted overall
    def weighted(mdf, col):
        w = mdf['n'].values; v = mdf[col].values; return float(np.average(v, weights=w)) if len(v)>0 else np.nan
    sumA = mdf_A.copy(); sumB = mdf_B.copy()
    kpi = pd.DataFrame([
        {'scenario':'A (com SP)','r2_w':weighted(sumA,'r2'),'rmse_w':weighted(sumA,'rmse'),'mae_w':weighted(sumA,'mae'), 'n_total':int(sumA['n'].sum())},
        {'scenario':'B (sem SP)','r2_w':weighted(sumB,'r2'),'rmse_w':weighted(sumB,'rmse'),'mae_w':weighted(sumB,'mae'), 'n_total':int(sumB['n'].sum())},
    ])
    kpi.to_csv(TABLES/'sp_comparison_kpi_summary.csv', index=False)

    # Executive card image
    fig, ax = plt.subplots(figsize=(10,4))
    ax.axis('off')
    t = 'Resumo Executivo — SP vs Resto do Brasil\n' \
        f"A (com SP): R²(peso)={kpi.loc[0,'r2_w']:.2f} | RMSE(peso)={kpi.loc[0,'rmse_w']:.1f} | MAE(peso)={kpi.loc[0,'mae_w']:.1f} | N={kpi.loc[0,'n_total']}\n" \
        f"B (sem SP): R²(peso)={kpi.loc[1,'r2_w']:.2f} | RMSE(peso)={kpi.loc[1,'rmse_w']:.1f} | MAE(peso)={kpi.loc[1,'mae_w']:.1f} | N={kpi.loc[1,'n_total']}\n" \
        'So What: Use SP quando ele adiciona previsibilidade (ΔR²>0,p<0,05) e avalie modelos separados onde remover SP reduz erro.\n' \
        'Metodologia: RF por cluster; anti‑leak no treino; IC 95% bootstrap; 80-20 split.'
    ax.text(0.01, 0.95, t, va='top', ha='left', fontsize=10)
    plt.tight_layout(); plt.savefig(PRES/'sp_vs_rest_executive_summary.png', dpi=220); plt.close()
except Exception as e:
    print('KPI card generation failed:', e)

# 8) Q-Q plots (top 4 clusters por n do cenário A)
try:
    import scipy.stats as stats
except Exception:
    stats = None

try:
    if stats is not None:
        top4 = mdf_A.sort_values('n', ascending=False)['cluster'].head(4).tolist()
        combined = pd.concat([pdf_A, pdf_B], ignore_index=True)
        for c in top4:
            sub = combined[combined['cluster']==int(c)].copy()
            if sub.empty: continue
            fig, axes = plt.subplots(1,2, figsize=(12,5))
            for i, sc in enumerate(['A','B']):
                s = sub[sub['scenario']==sc].copy()
                if len(s) < 20:
                    axes[i].text(0.1,0.5,'Amostra insuficiente', transform=axes[i].transAxes)
                    axes[i].axis('off'); continue
                resid = (s['y_pred'] - s['y_true']).values
                stats.probplot(resid, dist="norm", plot=axes[i])
                axes[i].set_title(('Com SP' if sc=='A' else 'Sem SP'))
            fig.suptitle(f'Q-Q Plot de Resíduos — {name_map.get(int(c))}\nNormalidade ≈ linhas retas; desvios sistemáticos indicam não-normalidade e menor confiabilidade')
            plt.tight_layout(rect=[0,0,1,0.90])
            plt.savefig(PRES/f'sp_vs_rest_qq_cluster_{int(c)}.png', dpi=220)
            plt.close()
except Exception as e:
    print('Q-Q generation failed:', e)

print('SP vs Rest comparison artifacts generated under presentation folder.')

