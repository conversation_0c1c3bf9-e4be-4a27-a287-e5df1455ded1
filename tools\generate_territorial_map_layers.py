from pathlib import Path
import pandas as pd
import numpy as np
# geopandas not required; using raw GeoJSON + hardcoded centroids
import matplotlib.pyplot as plt
import matplotlib as mpl
import plotly.graph_objects as go
import plotly.io as pio
import json
import requests

BASE = Path('.')
REPORTS = BASE/'reports'/'2025-08-15'
TABLES = REPORTS/'tables'
PLOTS = REPORTS/'plots'
PRESENT = PLOTS/'presentation'
PLOTS.mkdir(parents=True, exist_ok=True)
PRESENT.mkdir(parents=True, exist_ok=True)

DATA = BASE/'data'/'processed'/'features_engineered_regional.csv'
df = pd.read_csv(DATA, low_memory=False)

# Load labels
assign_path = TABLES/'cluster_assignments.csv'
if assign_path.exists():
    lab = pd.read_csv(assign_path)
    labels = lab['cluster'].values if 'cluster' in lab.columns else np.zeros(len(df), dtype=int)
    if len(labels) < len(df):
        labels = np.pad(labels, (0, len(df)-len(labels)), constant_values=labels[-1])
else:
    reg = [c for c in df.columns if 'REGIAO_CHILLI' in c]
    labels = np.argmax(df[reg].values, axis=1) if reg else np.zeros(len(df), dtype=int)

# Load business names
names_path = TABLES/'cluster_business_names.csv'
if names_path.exists():
    names_df = pd.read_csv(names_path)
else:
    # fallback generic
    u = sorted(pd.Series(labels).unique())
    names_df = pd.DataFrame({'cluster': u, 'business_name': [f'Cluster {int(x)}' for x in u]})
name_map = {int(r.cluster): r.business_name for _, r in names_df.iterrows()}

# Load metrics per cluster
metrics_path = TABLES/'supervised_by_cluster_metrics_ci.csv'
metrics_df = pd.read_csv(metrics_path) if metrics_path.exists() else pd.DataFrame()
met_map = {int(r.cluster): {'r2': r.r2, 'rmse': r.rmse, 'mae': r.mae} for _, r in metrics_df.iterrows()} if not metrics_df.empty else {}

# Aggregation key: prefer UF if available; else fallback to REGIAO_CHILLI
use_region = False
if 'UF' in df.columns and df['UF'].notna().sum() > 0 and df['UF'].astype(str).nunique(dropna=True) > 0:
    key_series = df['UF'].astype(str)
    key_name = 'UF'
else:
    region_cols = [c for c in df.columns if c.startswith('Dim_Lojas.REGIAO_CHILLI_')]
    if not region_cols:
        raise SystemExit('Neither UF nor REGIAO_CHILLI available for territorial map')
    idx = np.argmax(df[region_cols].values, axis=1)
    def col_to_region(col):
        return col.split('REGIAO_CHILLI_')[-1].strip()
    regions = [col_to_region(region_cols[i]) for i in idx]
    key_series = pd.Series(regions, name='REGIAO_CHILLI')
    key_name = 'REGIAO_CHILLI'
    use_region = True

clusters = sorted(pd.Series(labels).unique())
agg = pd.DataFrame({key_name: key_series, 'cluster': labels}).groupby([key_name,'cluster']).size().rename('count').reset_index()
pivot = agg.pivot(index=key_name, columns='cluster', values='count').fillna(0)
pivot['total'] = pivot.sum(axis=1)
for c in clusters:
    pivot[f'share_{int(c)}'] = pivot[c] / pivot['total'].replace({0:np.nan})

dom_cluster = []
for key, row in pivot.iterrows():
    cdom = int(row[clusters].astype(float).idxmax()) if len(clusters)>0 else 0
    dom_cluster.append((key, cdom))
dom_df = pd.DataFrame(dom_cluster, columns=[key_name,'cluster_dom'])
dom_df['business_name'] = dom_df['cluster_dom'].map(name_map)
dom_df['n_total'] = pivot['total'].reindex(dom_df[key_name]).values
# metrics linked by dominant cluster
if met_map:
    dom_df['r2'] = dom_df['cluster_dom'].map(lambda c: met_map.get(int(c),{}).get('r2', np.nan))
    dom_df['rmse'] = dom_df['cluster_dom'].map(lambda c: met_map.get(int(c),{}).get('rmse', np.nan))
    dom_df['mae'] = dom_df['cluster_dom'].map(lambda c: met_map.get(int(c),{}).get('mae', np.nan))
else:
    dom_df['r2']=np.nan; dom_df['rmse']=np.nan; dom_df['mae']=np.nan

# Geo data
url = 'https://raw.githubusercontent.com/codeforamerica/click_that_hood/master/public/data/brazil-states.geojson'
import requests as _rq
_geo_text = _rq.get(url).text
import json as _json
geo = _json.loads(_geo_text)
# build DataFrame-like from geojson properties
import pandas as _pd
props = [_f['properties'] for _f in geo['features']]
geom = [_f['geometry'] for _f in geo['features']]
_geo_df = _pd.DataFrame(props)
_geo_df['geometry'] = geom
name_to_uf = {
    'Acre':'AC','Alagoas':'AL','Amapá':'AP','Amazonas':'AM','Bahia':'BA','Ceará':'CE','Distrito Federal':'DF',
    'Espírito Santo':'ES','Goiás':'GO','Maranhão':'MA','Mato Grosso':'MT','Mato Grosso do Sul':'MS','Minas Gerais':'MG',
    'Pará':'PA','Paraíba':'PB','Paraná':'PR','Pernambuco':'PE','Piauí':'PI','Rio de Janeiro':'RJ','Rio Grande do Norte':'RN',
    'Rio Grande do Sul':'RS','Rondônia':'RO','Roraima':'RR','Santa Catarina':'SC','São Paulo':'SP','Sergipe':'SE','Tocantins':'TO'}
_geo_df['UF'] = _geo_df['name'].map(name_to_uf)
if use_region:
    # map REGIAO_CHILLI to UF groups (approx) to color at regional level
    # We will replicate the region value across all UFs in that region for visualization
    reg_to_ufs = {
        'CENTRO-OESTE': ['DF','GO','MT','MS'],
        'NORDESTE': ['AL','BA','CE','MA','PB','PE','PI','RN','SE'],
        'NORTE': ['AC','AP','AM','PA','RO','RR','TO'],
        'SUDESTE': ['ES','MG','RJ','SP'],
        'SUL': ['PR','RS','SC'],
        'SÃO PAULO': ['SP']
    }
    # Expand dom_df by UF set for each region key
    exp_rows = []
    for key, row in dom_df.iterrows():
        reg = row['REGIAO_CHILLI'] if 'REGIAO_CHILLI' in dom_df.columns else key
        ufs = reg_to_ufs.get(reg, [])
        for u in ufs:
            r = row.copy()
            r['UF'] = u
            exp_rows.append(r)
    if exp_rows:
        dom_df2 = pd.DataFrame(exp_rows)
    else:
        dom_df2 = dom_df.copy()
    merged = _geo_df.merge(dom_df2, on='UF', how='left')
else:
    merged = _geo_df.merge(dom_df, on='UF', how='left')

# quick centroid approximations by UF (for markers)
centroids = {
 'AC':(-70.0,-9.0),'AL':(-36.6,-9.6),'AP':(-51.8,1.4),'AM':(-64.7,-3.4),'BA':(-41.7,-12.5),'CE':(-39.6,-5.3),'DF':(-47.9,-15.8),'ES':(-40.3,-19.5),'GO':(-49.3,-15.9),
 'MA':(-45.2,-5.0),'MT':(-56.1,-12.6),'MS':(-54.2,-20.5),'MG':(-44.6,-18.5),'PA':(-52.8,-3.8),'PB':(-36.7,-7.2),'PR':(-51.5,-24.5),'PE':(-37.9,-8.4),'PI':(-42.7,-7.0),
 'RJ':(-42.6,-22.1),'RN':(-36.8,-5.7),'RS':(-53.2,-30.0),'RO':(-63.4,-10.8),'RR':(-61.3,2.0),'SC':(-50.9,-27.2),'SP':(-48.0,-22.1),'SE':(-37.4,-10.5),'TO':(-48.3,-10.2)
}
merged['lon'] = merged['UF'].map(lambda u: centroids.get(u, (-55,-15))[0])
merged['lat'] = merged['UF'].map(lambda u: centroids.get(u, (-55,-15))[1])

# ---------- Interactive (HTML) with multi-layers ----------
# base choropleth by business_name (dominant)
# safer: fetch raw geojson via requests to avoid non-serializable fields
raw = requests.get('https://raw.githubusercontent.com/codeforamerica/click_that_hood/master/public/data/brazil-states.geojson').text
geojson = json.loads(raw)
# color map for clusters
# Use a more distinct qualitative palette (tab20)
palette = mpl.colormaps.get_cmap('tab20') if hasattr(mpl, 'colormaps') else mpl.cm.get_cmap('tab20')
cluster_colors = {int(c): mpl.colors.to_hex(palette(int(i)%20)) for i, c in enumerate(clusters)}
name_colors = {name_map[c]: cluster_colors[c] for c in clusters}

zname = merged['business_name'].fillna('N/A')
# create a mapping name->int for color scale indices
unique_names = sorted(zname.dropna().unique())
name_to_idx = {n:i for i,n in enumerate(unique_names)}
color_scale = [(i/(max(1,len(unique_names)-1) or 1), name_colors.get(cname, '#cccccc')) for cname,i in name_to_idx.items()]
# Build base choropleth via categorical trick: map names to indices
zvals = zname.map(name_to_idx).fillna(-1)

fig = go.Figure()
# add legend entries for cluster colors
if len(unique_names) > 0:
    ref_lon = float(merged['lon'].dropna().iloc[0]) if 'lon' in merged.columns and merged['lon'].notna().any() else -55
    ref_lat = float(merged['lat'].dropna().iloc[0]) if 'lat' in merged.columns and merged['lat'].notna().any() else -15
    for n in unique_names:
        fig.add_trace(go.Scattergeo(lon=[ref_lon], lat=[ref_lat], mode='markers',
                                    marker=dict(size=10, color=name_colors.get(n, '#cccccc')),
                                    name=n, showlegend=True, visible='legendonly'))
fig.add_trace(go.Choropleth(
    geojson=geojson,
    locations=merged['name'],
    z=zvals,
    zmin=0, zmax=max(name_to_idx.values()) if name_to_idx else 1,
    featureidkey='properties.name',
    colorscale=[(k, v) for k,v in sorted(color_scale, key=lambda x: x[0])] if color_scale else 'Viridis',
    showscale=False,
    marker_line_color='white', marker_line_width=0.5,
    hovertemplate='<b>%{location}</b><br>UF: %{customdata[0]}<br>Cluster: %{customdata[1]}<extra></extra>',
    customdata=np.stack([merged['UF'], merged['business_name']], axis=1)
))

# density overlays: for top 2 clusters by total count, add share choropleths
total_per_cluster = agg.groupby('cluster')['count'].sum().sort_values(ascending=False)
top_clusters = list(total_per_cluster.head(2).index)
for c in top_clusters:
    if use_region:
        # Map regional shares to each UF
        uf_to_region = {u: reg for reg, ufs in reg_to_ufs.items() for u in ufs}
        share_list = []
        for u in merged['UF']:
            reg = uf_to_region.get(u, None)
            val = float(pivot.loc[reg, f'share_{int(c)}']) if (reg in pivot.index) else 0.0
            share_list.append(val)
        share = np.array(share_list)
    else:
        share = pivot[f'share_{int(c)}'].reindex(merged['UF']).fillna(0).values
    fig.add_trace(go.Choropleth(
        geojson=geojson,
        locations=merged['name'],
        z=share,
        featureidkey='properties.name',
        colorscale='Reds', zmin=0, zmax=share.max() or 1,
        showscale=False,
        hovertemplate=f'<b>%{{location}}</b><br>Share {name_map.get(int(c), f"Cluster {int(c)}")}: %{{z:.2f}}<extra></extra>',
        visible=False,
        name=f'Densidade {name_map.get(int(c), f"Cluster {int(c)}")}'
    ))

# performance markers at centroids (color by R2, size by total)
r2vals = merged['r2'].fillna(0)
size = (merged['n_total'].fillna(0) ** 0.5) * 2 + 6
fig.add_trace(go.Scattergeo(
    lon=merged['lon'], lat=merged['lat'],
    mode='markers+text', text=merged['UF'], textposition='top center',
    textfont=dict(size=10),
    marker=dict(
        size=((merged['n_total'].fillna(0) ** 0.5) * 2.8 + 8),
        color=r2vals,
        colorscale='Blues',
        cmin=float(np.nanmin(r2vals)) if np.isfinite(np.nanmin(r2vals)) else 0,
        cmax=float(np.nanmax(r2vals)) if np.isfinite(np.nanmax(r2vals)) else 0.5,
        line=dict(color='white', width=1.0),
        colorbar=dict(title='R²')
    ),
    hovertemplate='<b>%{text}</b><br>Cluster: %{customdata[0]}<br>R²:%{customdata[1]:.2f} RMSE:%{customdata[2]:.2f} MAE:%{customdata[3]:.2f}<br>Transações:%{customdata[4]:.0f}<extra></extra>',
    customdata=np.stack([merged['business_name'].fillna('N/A'), merged['r2'].fillna(np.nan), merged['rmse'].fillna(np.nan), merged['mae'].fillna(np.nan), merged['n_total'].fillna(0)], axis=1),
    name='Desempenho (R² cor) & Tamanho (símbolo)'
))

fig.update_geos(fitbounds="locations", visible=False)

# Buttons to toggle density overlays
buttons = [dict(label='Base', method='update', args=[{'visible':[True] + [False]*len(top_clusters) + [True]}])]
for i,c in enumerate(top_clusters):
    vis = [True] + [j==i for j in range(len(top_clusters))] + [True]
    buttons.append(dict(label=f'Densidade {name_map.get(int(c), f"Cluster {int(c)}")}', method='update', args=[{'visible': vis}]))

ttl = 'Mapa Territorial — Distribuição de Clusters e Desempenho por ' + ('Região' if use_region else 'UF')
fig.update_layout(
    title={'text': ttl + '\nFonte: features_engineered_regional.csv • Metodologia: dominância por chave geográfica; marcador: tamanho~n, cor~R²', 'x':0.5},
    updatemenus=[dict(active=0, buttons=buttons, x=0.02, y=0.98)],
    legend=dict(orientation='h', yanchor='bottom', y=-0.08, x=0.5, xanchor='center')
)

# callout annotations (ex.: concentração em SP)
try:
    for i, c in enumerate(top_clusters):
        sp_share = float(pivot.loc['SP', f'share_{int(c)}']) if ('SP' in pivot.index) else np.nan
        avg_share = float(pivot[f'share_{int(c)}'].mean()) if f'share_{int(c)}' in pivot.columns else np.nan
        if np.isfinite(sp_share) and np.isfinite(avg_share) and sp_share > 1.4 * avg_share and sp_share > 0.25:
            txt = f"{name_map.get(int(c), f'Cluster {int(c)}')} concentrado em SP ({sp_share:.0%} vs média {avg_share:.0%})"
            fig.add_annotation(x=0.02, y=0.90 - 0.06*i, xref='paper', yref='paper', text=txt,
                               showarrow=False, bgcolor='rgba(255,255,255,0.75)')
except Exception:
    pass

html_out = PLOTS/'territorial_clustering_map_multi.html'
fig.write_html(str(html_out))
print('Saved interactive map ->', html_out)

# ---------- Static PNG with multi-layers ----------
png_out = PLOTS/'territorial_clustering_map_multi.png'
try:
    # Larger figure for clarity (affects vector size before DPI)
    fig2, ax = plt.subplots(1,1, figsize=(14, 16))
    g2 = merged.loc[merged.geometry.notnull() & (~merged.geometry.is_empty)].copy()
    if g2.empty:
        raise RuntimeError('No valid geometries to render for static map')
    # base by dominant cluster name color
    bnames = g2['business_name'].fillna('N/A')
    unique_b = list(sorted(bnames.unique()))
    b_map = {b: mpl.colors.to_hex(palette(unique_names.index(b)%10) if b in unique_names else palette(0)) for b in unique_b}
    g2.plot(ax=ax, color=bnames.map(b_map), linewidth=0.5, edgecolor='white')

    # overlay density for top 2 clusters
    for c in top_clusters:
        share = pivot[f'share_{int(c)}'].reindex(g2['UF']).astype(float).fillna(0.0)
        if np.nanmax(share.values) > 0:
            g2.assign(_share=share.values).plot(ax=ax, column='_share', cmap='Reds', alpha=0.25, legend=False)

    # performance markers
    for _, row in g2.iterrows():
        if row['geometry'] is None:
            continue
        x, y = row['lon'], row['lat']
        r2 = row.get('r2', np.nan)
        n = row.get('n_total', 0) or 0
        size = (np.sqrt(n) * 0.4) + 6
        vmax = float(np.nanmax(g2['r2'])) if np.isfinite(np.nanmax(g2['r2'])) else 0.5
        plt.scatter([x], [y], s=size**2, c=[[r2 if not np.isnan(r2) else 0.0]], cmap='Blues', vmin=0, vmax=max(0.1, vmax), edgecolors='white', linewidths=0.6, zorder=5)
        ax.text(x, y, f"{row['UF']}", fontsize=9, ha='center', va='bottom', zorder=6, fontweight='bold', color='black', bbox=dict(boxstyle='round,pad=0.2', fc='white', ec='none', alpha=0.6))

    # legends
    from matplotlib.lines import Line2D
    legend_elements = [Line2D([0],[0], marker='s', color='w', label=b, markerfacecolor=col, markersize=10) for b,col in list(b_map.items())[:10]]
    leg1 = ax.legend(handles=legend_elements, title='Cluster (Nome de Negócio)', loc='lower left', fontsize=10, title_fontsize=11, frameon=True)
    ax.add_artist(leg1)

    # add colorbar for R2 markers
    _vmax = float(np.nanmax(g2['r2'])) if np.isfinite(np.nanmax(g2['r2'])) else 0.5
    norm = mpl.colors.Normalize(vmin=0, vmax=max(0.1, _vmax))
    sm = plt.cm.ScalarMappable(cmap='Blues', norm=norm); sm.set_array([])
    cbar = fig2.colorbar(sm, ax=ax, fraction=0.03, pad=0.02)
    cbar.ax.set_title('R²', fontsize=9)

    ax.set_title('Mapa Territorial — Clusters Dominantes, Densidade (top 2) e Desempenho por UF\nFonte: features_engineered_regional.csv; Modelo RF anti-vazamento; Símbolo: tamanho ~ transações, cor ~ R²', fontsize=11)
    ax.axis('off')
    plt.tight_layout()
    # Export at high DPI for presentation clarity
    plt.savefig(png_out, dpi=320, bbox_inches='tight')
    print('Saved static map ->', png_out)
except Exception as e:
    print('Static map generation failed, attempting Plotly static export:', e)
    try:
        # try with default engine (kaleido)
        pio.write_image(fig, str(png_out), scale=2)
        print('Saved Plotly static image ->', png_out)
    except Exception as e2:
        # try to force orca as fallback if available
        try:
            pio.write_image(fig, str(png_out), scale=2, engine='orca')
            print('Saved Plotly static image with orca ->', png_out)
        except Exception as e3:
            print('Plotly static export failed. Interactive HTML is available.', e2, e3)

