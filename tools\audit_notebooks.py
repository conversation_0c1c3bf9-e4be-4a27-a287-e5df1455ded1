from __future__ import annotations
import json
from pathlib import Path
from typing import List, Dict, Any
import nbformat as nbf

TARGET_NOTEBOOKS = [
    Path('notebooks/chilli_beans_analysis.ipynb'),
    Path('notebooks/model_comparison_colab.ipynb'),
]
TARGET_NOTEBOOKS += list(Path('notebooks/preprocessamento').glob('*.ipynb'))


def _cell_src(cell) -> str:
    try:
        return ''.join(cell.get('source', ''))
    except Exception:
        try:
            return ''.join(cell.source)
        except Exception:
            return ''


def audit_notebook(nb_path: Path) -> Dict[str, Any]:
    nb = nbf.read(nb_path, as_version=4)
    issues: List[Dict[str, Any]] = []

    # 1) Top-level title/context
    has_top_title = False
    for cell in nb.cells:
        if cell.cell_type == 'markdown':
            src = _cell_src(cell).strip()
            if src:
                has_top_title = src.startswith('# ')
                break
    if not has_top_title:
        issues.append({
            'notebook': str(nb_path),
            'cell': 0,
            'type': 'missing-top-title',
            'suggestion': 'Adicionar <PERSON> markdown inicial com título (# Título), objetivo e breve escopo.'
        })

    # 2) Iterate cells for code documentation and plots meta
    cells = nb.cells
    for i, cell in enumerate(cells):
        if cell.cell_type != 'code':
            continue
        src = _cell_src(cell)
        src_stripped = src.strip()
        # 2a) Code section without comments/docstring (heurística)
        n_lines = src_stripped.count('\n') + 1 if src_stripped else 0
        n_comment_lines = sum(1 for ln in src_stripped.splitlines() if ln.strip().startswith('#'))
        has_docstring = ('"""' in src_stripped) or ("'''" in src_stripped)
        if n_lines >= 10 and n_comment_lines < 2 and not has_docstring:
            issues.append({
                'notebook': str(nb_path),
                'cell': i,
                'type': 'low-comment-density',
                'suggestion': 'Adicionar comentários explicativos (ou docstring) às seções de código com mais de 10 linhas.'
            })
        # 2b) Missing context before first code cell
        if i == 0 or (i > 0 and cells[i-1].cell_type != 'markdown'):
            # Only flag early in the notebook
            if i < 3 and 'import' in src_stripped:
                issues.append({
                    'notebook': str(nb_path),
                    'cell': i,
                    'type': 'missing-context-before-code',
                    'suggestion': 'Inserir célula markdown antes desta célula explicando objetivo e o que será executado.'
                })
        # 2c) Plotting heuristics
        is_plot = (('plt.' in src) or ('sns.' in src)) and any(k in src for k in ['.plot(', '.figure(', '.bar', '.hist', '.kde', '.scatter', '.lineplot', '.displot', '.heatmap', '.boxplot'])
        triggers = any(k in src for k in ['.show(', '.savefig(', 'plt.savefig', 'plt.show'])
        if is_plot and triggers:
            if ('plt.title' not in src) and ('ax.set_title' not in src):
                issues.append({'notebook': str(nb_path), 'cell': i, 'type': 'missing-title', 'suggestion': 'Adicionar título descritivo ao gráfico (plt.title ou ax.set_title).'})
            if ('plt.xlabel' not in src) and ('ax.set_xlabel' not in src):
                issues.append({'notebook': str(nb_path), 'cell': i, 'type': 'missing-xlabel', 'suggestion': 'Adicionar label ao eixo X (plt.xlabel ou ax.set_xlabel).'})
            if ('plt.ylabel' not in src) and ('ax.set_ylabel' not in src):
                issues.append({'notebook': str(nb_path), 'cell': i, 'type': 'missing-ylabel', 'suggestion': 'Adicionar label ao eixo Y (plt.ylabel ou ax.set_ylabel).'})
            if (('plt.legend' not in src) and ('ax.legend' not in src)) and (('label=' in src) or ('hue=' in src)):
                issues.append({'notebook': str(nb_path), 'cell': i, 'type': 'missing-legend', 'suggestion': 'Incluir legenda clara quando houver múltiplas séries (plt.legend ou ax.legend).'})
            # Interpretation: if next cell is not markdown, flag interpretation missing
            if i+1 < len(cells) and cells[i+1].cell_type != 'markdown':
                issues.append({'notebook': str(nb_path), 'cell': i, 'type': 'needs-interpretation', 'suggestion': 'Adicionar célula markdown após o gráfico com interpretação dos resultados.'})

    return {
        'notebook': str(nb_path),
        'issues': issues,
    }


def main():
    audited: List[str] = []
    all_issues: List[Dict[str, Any]] = []

    for nb in TARGET_NOTEBOOKS:
        if nb.exists():
            res = audit_notebook(nb)
            audited.append(res['notebook'])
            all_issues.extend(res['issues'])

    out_dir_tables = Path('reports') / '2025-08-15' / 'tables'
    out_dir_tables.mkdir(parents=True, exist_ok=True)
    out_json = out_dir_tables / 'cleanup_audit.json'

    payload = {
        'audited': audited,
        'issues_count': len(all_issues),
        'issues': all_issues,
    }
    out_json.write_text(json.dumps(payload, ensure_ascii=False, indent=2), encoding='utf-8')

    # Count archived files
    archived_count = sum(1 for p in Path('.archive').rglob('*') if p.is_file()) if Path('.archive').exists() else 0

    # Build markdown report
    md_lines: List[str] = []
    md_lines.append('# Relatório de Limpeza e Auditoria de Notebooks')
    md_lines.append('')
    md_lines.append(f'- Arquivos arquivados: {archived_count}')
    md_lines.append(f'- Notebooks auditados: {len(audited)}')
    md_lines.append('')
    md_lines.append('## Notebooks auditados')
    for n in audited:
        md_lines.append(f'- {n}')
    md_lines.append('')
    md_lines.append('## Problemas encontrados (amostra)')
    for item in all_issues[:50]:
        md_lines.append(f"- [{item['type']}] {item['notebook']} (célula {item['cell']}): {item['suggestion']}")
    md_lines.append('')
    md_lines.append('## Recomendações prioritárias')
    md_lines.append('1. Garantir títulos, labels de eixos e legendas em todos os gráficos (consistência visual).')
    md_lines.append('2. Inserir título e contexto no topo de cada notebook (objetivo e escopo).')
    md_lines.append('3. Adicionar comentários explicativos em células de código extensas (>10 linhas).')
    md_lines.append('4. Incluir interpretação (markdown) logo após gráficos e tabelas relevantes.')

    out_md = out_dir_tables / 'cleanup_audit_report.md'
    out_md.write_text('\n'.join(md_lines), encoding='utf-8')

    print(json.dumps({'audited_count': len(audited), 'archived_count': archived_count, 'json': str(out_json), 'markdown': str(out_md), 'issues_count': len(all_issues)}, ensure_ascii=False))


if __name__ == '__main__':
    main()

