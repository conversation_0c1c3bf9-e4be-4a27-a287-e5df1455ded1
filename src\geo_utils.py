import unicodedata
from typing import Optional
import pandas as pd


def strip_accents(text: str) -> str:
    """Remove acentos preservando apenas caracteres ASCII básicos."""
    if text is None:
        return ""
    nfkd = unicodedata.normalize("NFKD", text)
    return "".join(ch for ch in nfkd if not unicodedata.combining(ch))


def normalize_city_name(name: Optional[str]) -> str:
    """Normaliza nomes de cidades para UPPERCASE sem acentos e espaços extras.

    Exemplos: "São Paulo" -> "SAO PAULO"; "  Rio  de Janeiro " -> "RIO DE JANEIRO".
    """
    if name is None:
        return ""
    s = str(name).strip()
    if not s:
        return ""
    s = strip_accents(s)
    # Normaliza espaços múltiplos
    s = " ".join(s.split())
    return s.upper()


def normalize_city_series(series: pd.Series) -> pd.Series:
    """Aplica normalize_city_name a uma Series, preservando nulos como strings vazias."""
    return series.apply(lambda x: normalize_city_name(x))
