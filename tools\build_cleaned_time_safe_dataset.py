from pathlib import Path
import sys
import pandas as pd

BASE = Path(__file__).resolve().parents[1]
sys.path.append(str(BASE))
from src.geo_utils import normalize_city_series

INP = BASE/'data'/'clean'/'cleaned_featured.csv'
OUT = BASE/'data'/'processed'/'features_cleaned_pre_territorial_time.csv'
REMOVED_LOG = BASE/'reports'/'2025-08-15'/'tables'/'cleaning_removed_columns.csv'

# Columns to drop due to leakage/const
DROP_LEAKAGE = [
    'valor_boxcox', 'sq_valor', 'ratio_Preco_Custo_over_valor',
    'store_avg_valor', 'store_avg_valor_boxcox'
]
DROP_CONSTANTS = ['ratio_qtd_over_qtd_log1p', 'Natureza_Operacao_VENDA DE MERCADORIA', 'month']

# Groups for collinearity
GROUPS_KEEP = {
    'qtd': ['qtd', 'qtd_log1p', 'sq_qtd'],
    'day': ['day', 'day_boxcox'],
    'is_weekend': ['is_weekend_True', 'is_weekend_False']
}

# Drop duplicated encodings and *_nan
DROP_PREFIXES = ['Dim_Produtos.GRUPO_CHILLI_']
DROP_SUFFIXES = ['_nan']


def compute_store_avg_t1(df: pd.DataFrame) -> pd.Series:
    """Média histórica por loja usando apenas dados anteriores (t-1).

    Fórmula estável/alinhada:
      avg_t1 = cumsum(valor).shift(1) / cumcount
    onde cumcount é 0,1,2,... por loja; quando 0 → NaN.
    """
    if not {'id_loja','data','valor'}.issubset(df.columns):
        raise SystemExit('Required columns missing for t-1 computation: id_loja, data, valor')
    # Sort to ensure chronological order within each store
    df_sorted = df.sort_values(['id_loja', 'data']).copy()
    s = pd.to_numeric(df_sorted['valor'], errors='coerce')
    grp = df_sorted.groupby('id_loja')
    prev_sum = grp['valor'].cumsum().shift(1)
    prev_cnt = grp.cumcount()
    avg_t1 = prev_sum / prev_cnt.replace(0, pd.NA)
    # Reindex back to original order
    avg_t1.index = df_sorted.index
    return avg_t1.reindex(df.index)


def drop_if_exists(df: pd.DataFrame, cols: list[str]) -> list[str]:
    removed = []
    for c in cols:
        if c in df.columns:
            df.drop(columns=[c], inplace=True)
            removed.append(c)
    return removed


def main():
    if not INP.exists():
        raise SystemExit(f'Input not found: {INP}')

    df = pd.read_csv(INP, parse_dates=['data'], low_memory=False)

    removed = []

    # Compute safe store avg valor (t-1)
    df['store_avg_valor_t1'] = compute_store_avg_t1(df)

    # Remove leakage columns (including previous store_avg_valor*)
    removed += drop_if_exists(df, DROP_LEAKAGE)

    # Remove constants
    removed += drop_if_exists(df, DROP_CONSTANTS)

    # Group drops
    if 'qtd' in df.columns:
        removed += drop_if_exists(df, [c for c in GROUPS_KEEP['qtd'] if c != 'qtd'])
    if 'day' in df.columns:
        removed += drop_if_exists(df, [c for c in GROUPS_KEEP['day'] if c != 'day'])
    if 'is_weekend_True' in df.columns:
        removed += drop_if_exists(df, [c for c in GROUPS_KEEP['is_weekend'] if c != 'is_weekend_True'])

    # Drop duplicated encodings and *_nan
    removed += drop_if_exists(df, [c for c in df.columns if any(c.startswith(p) for p in DROP_PREFIXES)])
    removed += drop_if_exists(df, [c for c in df.columns if any(c.endswith(s) for s in DROP_SUFFIXES)])

    # Add cidade std
    if 'cidade' in df.columns:
        df['Cidade_std'] = normalize_city_series(df['cidade'])
    else:
        df['Cidade_std'] = ''

    # Save outputs
    OUT.parent.mkdir(parents=True, exist_ok=True)
    df.to_csv(OUT, index=False)

    # Log removed columns
    REMOVED_LOG.parent.mkdir(parents=True, exist_ok=True)
    pd.DataFrame({'removed_column': sorted(set(removed))}).to_csv(REMOVED_LOG, index=False)

    print('Saved:', OUT)
    print('Removed columns:', len(set(removed)))


if __name__ == '__main__':
    main()

