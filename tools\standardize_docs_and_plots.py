from __future__ import annotations
from pathlib import Path
from typing import List, Dict, Any
import nbformat as nbf

NOTEBOOKS = [
    Path('notebooks/chilli_beans_analysis.ipynb'),
    Path('notebooks/model_comparison_colab.ipynb'),
    Path('notebooks/preprocessamento/eda_distribuicoes.ipynb'),
    Path('notebooks/preprocessamento/eda_geografico.ipynb'),
]

STYLE_CODE = (
    "# [auto-doc] estilo global\n"
    "import matplotlib as mpl, seaborn as sns\n"
    "import matplotlib.pyplot as plt\n"
    "sns.set_theme(style='whitegrid', context='notebook', palette='deep')\n"
    "mpl.rcParams.update({'figure.dpi': 150, 'axes.titlesize': 12, 'axes.labelsize': 11, 'legend.fontsize': 10})\n"
)

CONTEXT_BY_FILE: Dict[str, str] = {
    'chilli_beans_analysis.ipynb': (
        "# Análise Principal – Chilli Beans\n\n"
        "Este notebook executa o pipeline de análise com validações e geração de artefatos (plots/tabelas/modelo)."
    ),
    'model_comparison_colab.ipynb': (
        "# Comparação de Modelos – Colab\n\n"
        "Compara algoritmos (baseline e tuning), gera métricas comparativas e explicabilidade (SHAP/importâncias)."
    ),
    'eda_distribuicoes.ipynb': (
        "# EDA – Distribuições e Correlações\n\n"
        "Inspeções de distribuições, outliers e correlações iniciais do dataset limpo."
    ),
    'eda_geografico.ipynb': (
        "# EDA – Geográfico\n\n"
        "Análises por UF/cidade, mapas e sumários regionais."
    ),
}

DOC_TEMPLATE = (
    "### [auto-doc] Etapa {n}\n"
    "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n\n"
    "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n\n"
    "**Dados:** [Input → Output esperado]\n\n"
)

INTERP_TEMPLATE = (
    "### [auto-doc] Interpretação\n\n"
    "**Interpretação:** Descreva padrões e insights observados.\n\n"
    "**Implicações:** Qual o impacto/uso para o negócio.\n\n"
    "**Próximos passos:** Como usar ou aprofundar esta análise.\n"
)

PLOT_APPEND_HEADER = "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n"


def _cell_src(cell) -> str:
    try:
        return ''.join(cell.get('source', ''))
    except Exception:
        try:
            return ''.join(cell.source)
        except Exception:
            return ''


def ensure_style_and_title(nb: nbf.NotebookNode, fname: str) -> None:
    # Ensure style cell at top (only once)
    has_style = any(c.cell_type == 'code' and '[auto-doc] estilo global' in _cell_src(c) for c in nb.cells[:3])
    if not has_style:
        nb.cells.insert(0, nbf.v4.new_code_cell(STYLE_CODE))
    # Ensure top markdown with title/context
    has_top_md = any(c.cell_type == 'markdown' for c in nb.cells[:2])
    if not has_top_md:
        ctx = CONTEXT_BY_FILE.get(fname, '# Notebook\n\nDescrição geral do objetivo e escopo.')
        nb.cells.insert(0, nbf.v4.new_markdown_cell(ctx))


def is_plot_cell(src: str) -> bool:
    if not src:
        return False
    triggers = ['plt.show', 'plt.savefig', '.plot(', '.figure(', '.bar(', '.hist(', '.kde', '.scatter', 'sns.', 'heatmap', 'boxplot', 'lineplot', 'displot']
    return any(t in src for t in triggers)


def has_title(src: str) -> bool:
    return ('plt.title' in src) or ('ax.set_title' in src)


def has_xlabel(src: str) -> bool:
    return ('plt.xlabel' in src) or ('ax.set_xlabel' in src)


def has_ylabel(src: str) -> bool:
    return ('plt.ylabel' in src) or ('ax.set_ylabel' in src)


def has_legend(src: str) -> bool:
    return ('plt.legend' in src) or ('ax.legend' in src)


def standardize_notebook(path: Path) -> None:
    nb = nbf.read(path, as_version=4)
    ensure_style_and_title(nb, path.name)

    step = 1
    i = 0
    new_cells = []
    while i < len(nb.cells):
        cell = nb.cells[i]
        if cell.cell_type == 'code':
            # Insert doc markdown before code cell if missing or not auto-doc
            prev = new_cells[-1] if new_cells else None
            need_doc = True
            if prev is not None and prev.cell_type == 'markdown' and '[auto-doc] Etapa' in _cell_src(prev):
                need_doc = False
            if need_doc:
                md = DOC_TEMPLATE.format(n=step)
                new_cells.append(nbf.v4.new_markdown_cell(md))
                step += 1
            # Patch plotting annotations if needed
            src = _cell_src(cell)
            if is_plot_cell(src):
                append_lines: List[str] = []
                if not has_title(src):
                    append_lines.append("plt.title('Gráfico – título descritivo (ajustar)')")
                if not has_xlabel(src):
                    append_lines.append("plt.xlabel('Eixo X (ajustar)')")
                if not has_ylabel(src):
                    append_lines.append("plt.ylabel('Eixo Y (ajustar)')")
                if (('label=' in src) or ('hue=' in src)) and not has_legend(src):
                    append_lines.append("plt.legend()")
                if append_lines:
                    patched = src
                    if PLOT_APPEND_HEADER not in src:
                        patched += ('\n' + PLOT_APPEND_HEADER)
                    patched += ('\n'.join(['import matplotlib.pyplot as plt'] + append_lines))
                    cell.source = patched
            new_cells.append(cell)
            # Add interpretation markdown after plot cells if not already present next
            if is_plot_cell(_cell_src(cell)):
                # Lookahead next original cell (i+1), but we build new_cells; we can conservatively insert here
                next_is_md = False
                if i + 1 < len(nb.cells):
                    nxt = nb.cells[i+1]
                    next_is_md = (nxt.cell_type == 'markdown' and ('[auto-doc] Interpretação' in _cell_src(nxt) or '### Interpretação' in _cell_src(nxt)))
                if not next_is_md:
                    new_cells.append(nbf.v4.new_markdown_cell(INTERP_TEMPLATE))
        else:
            new_cells.append(cell)
        i += 1

    nb.cells = new_cells
    nbf.write(nb, path)
    print(f'Standardized: {path}')


def main():
    for p in NOTEBOOKS:
        if p.exists():
            try:
                standardize_notebook(p)
            except Exception as e:
                print(f'Failed to standardize {p}: {e}')
        else:
            print(f'Skip missing: {p}')


if __name__ == '__main__':
    main()

